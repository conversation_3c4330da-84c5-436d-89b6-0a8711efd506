"""
Serviço de recomendações desacoplado do Streamlit, lendo CSV e retornando
cores recomendadas (BGR) e a estação detectada, com utilitários para relatório.
"""
from __future__ import annotations

import os
from dataclasses import dataclass
from typing import List, Tuple, Dict, Any
import pandas as pd


def _find_catalog_csv() -> str | None:
    base_dir = os.path.dirname(__file__)
    candidates = [
        os.path.abspath(os.path.join(base_dir, '../../data/catalogo_roupas.csv')),
        os.path.abspath(os.path.join(base_dir, '../data/catalogo_roupas.csv')),
        os.path.abspath(os.path.join(os.getcwd(), 'data', 'catalogo_roupas.csv')),
        os.path.abspath('catalogo_roupas.csv'),
    ]
    for p in candidates:
        if os.path.exists(p):
            return p
    return None


@dataclass
class RecommendationResult:
    colors_bgr: List[List[int]]
    season: str | None


def generate_recommendations(measurements: Dict[str, Any]) -> RecommendationResult:
    path = _find_catalog_csv()
    if not path:
        return RecommendationResult(colors_bgr=[], season=None)

    catalog = pd.read_csv(path)
    catalog.columns = catalog.columns.str.strip().str.lower()
    filtered = catalog.copy()

    subtom = (measurements.get('Subtom') or '').lower()
    contraste = (measurements.get('Classificação') or '').lower()
    intensidade = (measurements.get('Intensidade') or '').lower()
    profundidade = (measurements.get('Profundidade') or '').lower()

    season = None
    if 'estação' in filtered.columns:
        if subtom == 'quente':
            if intensidade == 'alta' and profundidade == 'claro':
                season = 'primavera brilhante'
            elif intensidade == 'baixa':
                season = 'outono suave' if profundidade == 'escuro' else 'primavera pura'
            elif intensidade == 'média':
                season = 'primavera clara' if profundidade == 'claro' else 'outono puro'
        elif subtom == 'frio':
            if intensidade == 'alta' and (contraste in ['médio contraste', 'baixo contraste escuro']):
                season = 'inverno brilhante'
            elif intensidade == 'baixa':
                season = 'verão suave' if profundidade == 'claro' else 'inverno profundo'
            elif intensidade == 'média':
                season = 'verão claro' if profundidade == 'claro' else 'inverno puro'
        elif subtom == 'neutro':
            season = 'verão suave' if profundidade == 'claro' else 'outono suave'
        elif subtom == 'oliva':
            season = 'primavera pura' if profundidade == 'claro' else 'outono profundo'

        if season:
            filtered = filtered[filtered['estação'].str.contains(season, case=False, na=False)]

    colors_bgr: List[List[int]] = []
    if 'cor bgr' in filtered.columns:
        filtered['cor bgr'] = filtered['cor bgr'].apply(
            lambda x: list(map(int, str(x).strip('[]').split())) if pd.notna(x) else [0, 0, 0]
        )
        for _, row in filtered.iterrows():
            if isinstance(row['cor bgr'], list) and len(row['cor bgr']) == 3:
                colors_bgr.append(row['cor bgr'])

    return RecommendationResult(colors_bgr=colors_bgr, season=season)


def build_text_report(colors_bgr: List[List[int]], measurements: Dict[str, Any]) -> str:
    lines = []
    lines.append('RELATÓRIO DE ANÁLISE DE COLORAÇÃO PESSOAL')
    lines.append('=' * 50)
    lines.append('')
    lines.append('ANÁLISE PESSOAL:')
    lines.append('-' * 20)
    for key in ['Classificação', 'Subtom', 'Tom de pele (escala 0-10)', 'Tom de cabelo (escala 0-10)', 'Tom dos olhos (escala 0-10)', 'Intensidade', 'Profundidade']:
        if key in measurements:
            lines.append(f"{key}: {measurements.get(key)}")
    lines.append('')
    lines.append('CORES RECOMENDADAS (RGB):')
    lines.append('-' * 30)
    for i, bgr in enumerate(colors_bgr, start=1):
        rgb = (bgr[2], bgr[1], bgr[0])
        hex_code = f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"
        lines.append(f"Cor {i:2d}: RGB{rgb} - HEX: {hex_code}")
    lines.append("")
    lines.append(f"Total de cores recomendadas: {len(colors_bgr)}")
    lines.append('')
    lines.append('Relatório gerado automaticamente pelo sistema de análise de coloração pessoal.')
    return "\n".join(lines)


