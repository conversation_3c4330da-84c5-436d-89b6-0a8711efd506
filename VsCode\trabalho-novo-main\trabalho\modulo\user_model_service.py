"""
Serviço integrado para gerenciamento de modelos base de usuários
Combina storage, processamento e try-on em uma interface unificada
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from PIL import Image
import cv2
import numpy as np

from storage_service import get_storage_service, ArtifactType
from processing_services import get_processing_manager
from huggingface_service import get_hf_service

logger = logging.getLogger(__name__)

class UserModelService:
    """Serviço principal para gerenciamento de modelos base de usuários"""
    
    def __init__(self):
        self.storage = get_storage_service()
        self.processing = get_processing_manager()
        self.hf_service = get_hf_service()
        self._initialized = False
    
    async def initialize(self):
        """Inicializa todos os serviços dependentes"""
        if not self._initialized:
            await self.processing.initialize_all()
            self._initialized = True
            logger.info("UserModelService inicializado")
    
    async def create_user_model(self, user_image: Image.Image, 
                              filename: str = "user_image.png") -> Dict[str, Any]:
        """
        Fase 1: Cria modelo base completo do usuário
        
        Args:
            user_image: Imagem PIL do usuário
            filename: Nome do arquivo original
            
        Returns:
            Dict com informações do modelo criado
        """
        await self.initialize()
        start_time = datetime.now()
        
        try:
            logger.info(f"Criando modelo base para: {filename}")
            
            # Cria entrada no storage
            model_id = await self.storage.create_user_model(user_image, filename)
            
            # Converte para OpenCV
            cv_image = cv2.cvtColor(np.array(user_image), cv2.COLOR_RGB2BGR)
            
            # Processamento pesado (pose, segmentação, inpainting)
            processing_results = await self.processing.process_user_image(cv_image)
            
            # Salva artefatos processados
            artifacts_saved = {}
            
            # Mapa de pose
            if processing_results["pose"]["pose_map"] is not None:
                pose_path = await self.storage.save_artifact(
                    model_id, ArtifactType.POSE_MAP, processing_results["pose"]["pose_map"]
                )
                artifacts_saved["pose_map"] = str(pose_path)
            
            # Máscara de segmentação
            if processing_results["segmentation"]["mask"] is not None:
                mask_path = await self.storage.save_artifact(
                    model_id, ArtifactType.SEGMENTATION_MASK, 
                    processing_results["segmentation"]["mask"]
                )
                artifacts_saved["segmentation_mask"] = str(mask_path)
            
            # Imagem limpa (inpainting)
            if processing_results["inpainting"]["inpainted_image"] is not None:
                clean_path = await self.storage.save_artifact(
                    model_id, ArtifactType.CLEAN_IMAGE, 
                    processing_results["inpainting"]["inpainted_image"]
                )
                artifacts_saved["clean_image"] = str(clean_path)
            
            # Atualiza metadados com tempo de processamento
            metadata = await self.storage.get_metadata(model_id)
            processing_time = (datetime.now() - start_time).total_seconds()
            metadata.processing_time = processing_time
            await self.storage._save_metadata(model_id, metadata)
            
            # Carrega imagem original para resposta
            original_path = await self.storage.load_artifact(model_id, ArtifactType.ORIGINAL)
            
            result = {
                "userModelId": model_id,
                "initialImage": str(original_path),
                "processingTime": processing_time,
                "artifacts": artifacts_saved,
                "processingQuality": processing_results["processing_quality"],
                "message": "Modelo base criado com sucesso"
            }
            
            logger.info(f"Modelo base criado: {model_id} ({processing_time:.2f}s)")
            return result
            
        except Exception as e:
            logger.error(f"Erro ao criar modelo base: {e}", exc_info=True)
            # Cleanup em caso de erro
            if 'model_id' in locals():
                await self.storage.delete_model(model_id)
            raise
    
    async def try_on_clothing(self, model_id: str, clothing_image: Image.Image,
                            clothing_description: str = "Roupa enviada pelo usuário") -> Dict[str, Any]:
        """
        Fase 2: Aplica roupa usando modelo base existente
        
        Args:
            model_id: ID do modelo base
            clothing_image: Imagem PIL da roupa
            clothing_description: Descrição da roupa
            
        Returns:
            Dict com resultado do try-on
        """
        await self.initialize()
        start_time = datetime.now()
        
        try:
            logger.info(f"Iniciando try-on para modelo: {model_id}")
            
            # Verifica se modelo existe
            if not await self.storage.model_exists(model_id):
                raise ValueError(f"Modelo {model_id} não encontrado")
            
            # Carrega imagem original do usuário (já processada na Fase 1)
            user_image = await self.storage.load_artifact(model_id, ArtifactType.ORIGINAL)
            
            # Aplica try-on usando HuggingFace (rápido, usa cache)
            result_path = await self.hf_service.try_on_clothing(
                user_image, clothing_image, clothing_description
            )
            
            if result_path is None:
                raise RuntimeError("Falha na geração do try-on")
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                "resultImage": result_path,
                "processingTime": processing_time,
                "userModelId": model_id,
                "clothingDescription": clothing_description,
                "message": "Try-on realizado com sucesso"
            }
            
            logger.info(f"Try-on concluído: {model_id} ({processing_time:.2f}s)")
            return result
            
        except Exception as e:
            logger.error(f"Erro no try-on: {e}", exc_info=True)
            raise
    
    async def get_model_info(self, model_id: str) -> Dict[str, Any]:
        """Retorna informações detalhadas do modelo"""
        if not await self.storage.model_exists(model_id):
            raise ValueError(f"Modelo {model_id} não encontrado")
        
        metadata = await self.storage.get_metadata(model_id)
        return metadata.to_dict()
    
    async def delete_model(self, model_id: str) -> bool:
        """Remove modelo e todos os artefatos"""
        return await self.storage.delete_model(model_id)
    
    async def list_models(self, limit: int = 50) -> Dict[str, Any]:
        """Lista modelos disponíveis"""
        models = await self.storage.list_models(limit)
        return {
            "models": [model.to_dict() for model in models],
            "total": len(models)
        }
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do sistema"""
        storage_stats = self.storage.get_storage_stats()
        hf_stats = self.hf_service.get_cache_stats()
        
        return {
            "storage": storage_stats,
            "huggingface_cache": hf_stats,
            "timestamp": datetime.now().isoformat()
        }
    
    async def cleanup_old_models(self, max_age_days: int = 30) -> int:
        """Remove modelos antigos"""
        return await self.storage.cleanup_old_models(max_age_days)
    
    async def optimize_model(self, model_id: str) -> Dict[str, Any]:
        """
        Otimiza modelo existente (reprocessa com melhor qualidade se necessário)
        """
        if not await self.storage.model_exists(model_id):
            raise ValueError(f"Modelo {model_id} não encontrado")
        
        # Carrega imagem original
        original_image = await self.storage.load_artifact(model_id, ArtifactType.ORIGINAL)
        
        # Reprocessa com configurações otimizadas
        cv_image = cv2.cvtColor(np.array(original_image), cv2.COLOR_RGB2BGR)
        processing_results = await self.processing.process_user_image(cv_image)
        
        # Atualiza artefatos se a qualidade melhorou
        current_metadata = await self.storage.get_metadata(model_id)
        improvements = []
        
        # Verifica se pose melhorou
        if processing_results["processing_quality"]["pose_confidence"] > 0.7:
            await self.storage.save_artifact(
                model_id, ArtifactType.POSE_MAP, processing_results["pose"]["pose_map"]
            )
            improvements.append("pose")
        
        # Verifica se segmentação melhorou
        if processing_results["processing_quality"]["segmentation_confidence"] > 0.8:
            await self.storage.save_artifact(
                model_id, ArtifactType.SEGMENTATION_MASK, 
                processing_results["segmentation"]["mask"]
            )
            improvements.append("segmentation")
        
        # Atualiza metadados
        current_metadata.updated_at = datetime.now()
        await self.storage._save_metadata(model_id, current_metadata)
        
        return {
            "model_id": model_id,
            "improvements": improvements,
            "new_quality": processing_results["processing_quality"],
            "message": f"Modelo otimizado com {len(improvements)} melhorias"
        }

# Instância global do serviço
_user_model_service: Optional[UserModelService] = None

def get_user_model_service() -> UserModelService:
    """Retorna instância singleton do serviço de modelos de usuário"""
    global _user_model_service
    if _user_model_service is None:
        _user_model_service = UserModelService()
    return _user_model_service

# Funções de conveniência para uso direto
async def create_user_model(user_image: Image.Image, filename: str = "user_image.png") -> Dict[str, Any]:
    """Função de conveniência para criar modelo base"""
    service = get_user_model_service()
    return await service.create_user_model(user_image, filename)

async def try_on_clothing(model_id: str, clothing_image: Image.Image, 
                         description: str = "Roupa enviada pelo usuário") -> Dict[str, Any]:
    """Função de conveniência para try-on"""
    service = get_user_model_service()
    return await service.try_on_clothing(model_id, clothing_image, description)
