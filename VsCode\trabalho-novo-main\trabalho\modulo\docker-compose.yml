# VesteAI - Docker Compose para Produção
version: '3.8'

services:
  # Aplicação principal
  vesteai-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vesteai-app
    restart: unless-stopped
    ports:
      - "8501:8501"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - REDIS_URL=redis://redis:6379
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=vesteai
      - DB_USER=vesteai
      - DB_PASSWORD=${DB_PASSWORD:-vesteai123}
      - HF_TOKEN=${HF_TOKEN}
      - USE_GPU=${USE_GPU:-false}
      - MAX_CONCURRENT_REQUESTS=20
    volumes:
      - ./data:/app/data:ro
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./cache:/app/cache
    depends_on:
      - redis
      - postgres
    networks:
      - vesteai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Redis para cache
  redis:
    image: redis:7-alpine
    container_name: vesteai-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    networks:
      - vesteai-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # PostgreSQL para dados persistentes (opcional)
  postgres:
    image: postgres:15-alpine
    container_name: vesteai-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=vesteai
      - POSTGRES_USER=vesteai
      - POSTGRES_PASSWORD=${DB_PASSWORD:-vesteai123}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - vesteai-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U vesteai -d vesteai"]
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Nginx como reverse proxy e load balancer
  nginx:
    image: nginx:alpine
    container_name: vesteai-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - vesteai-app
    networks:
      - vesteai-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.2'

  # Prometheus para métricas (opcional)
  prometheus:
    image: prom/prometheus:latest
    container_name: vesteai-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - vesteai-network
    profiles:
      - monitoring

  # Grafana para visualização (opcional)
  grafana:
    image: grafana/grafana:latest
    container_name: vesteai-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - vesteai-network
    profiles:
      - monitoring

volumes:
  redis-data:
    driver: local
  postgres-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  vesteai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
