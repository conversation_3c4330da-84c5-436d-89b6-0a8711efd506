"""
VesteAI - Versão Ultra Otimizada
Aplicação com arquitetura moderna, alta performance e escalabilidade
"""
import asyncio
import io
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import uuid
import os # Added for os.path.exists and os.path.getsize

import streamlit as st
import numpy as np
from PIL import Image

# Imports dos módulos otimizados
from config_manager import get_config, ConfigManager
from dependency_injection import get_container, configure_dependencies, inject
from advanced_logging import get_logger, log_performance, configure_logging, log_context
from advanced_cache_service import AdvancedCacheService
from optimized_image_processor import OptimizedImageProcessor
from optimized_data_processor import OptimizedColorAnalyzer, DataValidator
from recommender_service import generate_recommendations, build_text_report
from huggingface_service import virtual_try_on_streamlit
from tryon_service import TryOnService

logger = get_logger(__name__)


class SessionManager:
    """Gerenciador de sessão otimizado"""
    
    @staticmethod
    def get_session_id() -> str:
        """Obtém ou cria ID da sessão"""
        if 'session_id' not in st.session_state:
            st.session_state.session_id = str(uuid.uuid4())
        return st.session_state.session_id
    
    @staticmethod
    def get_user_context() -> Dict[str, Any]:
        """Obtém contexto do usuário"""
        return {
            'session_id': SessionManager.get_session_id(),
            'user_agent': st.context.headers.get('User-Agent', 'Unknown'),
            'timestamp': datetime.now().isoformat()
        }


class UIComponents:
    """Componentes de UI otimizados e reutilizáveis"""
    
    def __init__(self, config):
        self.config = config
    
    def render_header(self):
        """Renderiza cabeçalho da aplicação"""
        st.markdown(f"""
        <div style="text-align: center; padding: 2rem 0;">
            <h1 style="color: {self.config.colors['primary_text']}; font-size: 3rem; margin-bottom: 0;">
                🎨 {self.config.app_name}
            </h1>
            <p style="color: {self.config.colors['secondary_text']}; font-size: 1.2rem;">
                Análise de Coloração Pessoal com IA - Versão {self.config.version}
            </p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_sidebar(self, cache_service: AdvancedCacheService):
        """Renderiza sidebar com informações e controles"""
        with st.sidebar:
            st.header("📋 Como Usar")
            st.markdown("""
            **Passos simples:**
            1. 📸 Faça upload de sua foto
            2. ⏳ Aguarde a análise da IA
            3. 🎨 Descubra sua paleta ideal
            4. 👗 Veja recomendações personalizadas
            
            **Dicas para melhor resultado:**
            - Foto com boa iluminação natural
            - Rosto bem visível e centralizado
            - Fundo neutro ou uniforme
            - Evite filtros ou edições
            """)
            
            st.divider()
            
            # Métricas da aplicação
            st.header("📊 Estatísticas")
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🔄 Atualizar"):
                    st.rerun()
            
            with col2:
                if st.button("🗑️ Limpar Cache"):
                    asyncio.run(cache_service.clear())
                    st.success("Cache limpo!")
            
            # Mostra estatísticas do cache
            cache_stats = cache_service.get_stats()
            st.metric("Taxa de Acerto", f"{cache_stats['hit_rate']:.1%}")
            st.metric("Itens em Cache", cache_stats['hits'] + cache_stats['misses'])
            
            st.divider()
            st.caption(f"Desenvolvido com ❤️ | Versão {self.config.version}")
    
    def render_loading_animation(self, message: str = "Processando..."):
        """Renderiza animação de carregamento"""
        return st.spinner(f"🤖 {message}")
    
    def render_error_message(self, error: str, details: str = None):
        """Renderiza mensagem de erro amigável"""
        st.error(f"❌ {error}")
        if details and self.config.debug:
            with st.expander("Detalhes técnicos"):
                st.code(details)
    
    def render_success_message(self, message: str):
        """Renderiza mensagem de sucesso"""
        st.success(f"✅ {message}")
    
    def render_metrics_dashboard(self, metrics: Dict[str, Any]):
        """Renderiza dashboard de métricas"""
        st.subheader("📈 Métricas de Performance")
        
        cols = st.columns(4)
        
        with cols[0]:
            st.metric(
                "Tempo de Processamento",
                f"{metrics.get('processing_time', 0):.2f}s"
            )
        
        with cols[1]:
            st.metric(
                "Confiança da Análise",
                f"{metrics.get('confidence', 0):.1%}"
            )
        
        with cols[2]:
            st.metric(
                "Cores Detectadas",
                metrics.get('colors_detected', 0)
            )
        
        with cols[3]:
            st.metric(
                "Qualidade da Imagem",
                metrics.get('image_quality', 'N/A')
            )


class VesteAIUltraOptimized:
    """Aplicação principal ultra otimizada"""
    
    def __init__(self):
        # Configuração inicial
        configure_logging()
        configure_dependencies()
        
        self.config = get_config()
        self.container = get_container()
        self.ui = UIComponents(self.config)
        self.tryon = TryOnService()
        
        # Inicializa serviços
        self._initialize_services()
        
        logger.info("VesteAI Ultra Otimizado inicializado")
    
    def _initialize_services(self):
        """Inicializa serviços da aplicação"""
        try:
            # Cache service
            self.cache_service = AdvancedCacheService(
                redis_url=self.config.cache.redis_url,
                enable_compression=True
            )
            
            # Image processor
            self.image_processor = OptimizedImageProcessor(self.cache_service)
            
            # Color analyzer
            self.color_analyzer = OptimizedColorAnalyzer(self.cache_service)
            
            # Registra serviços no container DI
            self.container.register_instance(AdvancedCacheService, self.cache_service)
            self.container.register_instance(OptimizedImageProcessor, self.image_processor)
            self.container.register_instance(OptimizedColorAnalyzer, self.color_analyzer)
            
        except Exception as e:
            logger.error(f"Erro ao inicializar serviços: {e}")
            raise
    
    def _configure_streamlit(self):
        """Configura Streamlit com otimizações"""
        st.set_page_config(
            page_title=f"{self.config.app_name} - Análise de Coloração",
            page_icon="🎨",
            layout="wide",
            initial_sidebar_state="expanded",
            menu_items={
                'Get Help': None,
                'Report a bug': None,
                'About': f"{self.config.app_name} v{self.config.version}"
            }
        )
        
        # CSS customizado otimizado
        self._inject_custom_css()
    
    def _inject_custom_css(self):
        """Injeta CSS customizado otimizado"""
        css = f"""
        <style>
            /* Otimizações de performance */
            .stApp {{
                background-color: {self.config.colors['background']};
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }}
            
            /* Header otimizado */
            .main-header {{
                color: {self.config.colors['primary_text']};
                text-align: center;
                font-weight: 600;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            }}
            
            /* Sidebar otimizada */
            .stSidebar > div:first-child {{
                background: linear-gradient(180deg, {self.config.colors['sidebar']} 0%, 
                           {self.config.colors['background']} 100%);
                border-right: 2px solid {self.config.colors['primary_text']}20;
            }}
            
            /* Botões otimizados */
            .stButton > button {{
                background: linear-gradient(45deg, {self.config.colors['button']}, 
                           {self.config.colors['button_hover']});
                color: white;
                border: none;
                border-radius: 25px;
                padding: 0.6rem 1.5rem;
                font-weight: 600;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            }}
            
            .stButton > button:hover {{
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            }}
            
            /* Métricas otimizadas */
            .metric-container {{
                background: white;
                padding: 1rem;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                border-left: 4px solid {self.config.colors['button']};
            }}
            
            /* Animações suaves */
            .stProgress > div > div {{
                background: linear-gradient(90deg, {self.config.colors['button']}, 
                           {self.config.colors['button_hover']});
            }}
            
            /* Responsividade */
            @media (max-width: 768px) {{
                .main-header {{
                    font-size: 2rem !important;
                }}
            }}
        </style>
        """
        st.markdown(css, unsafe_allow_html=True)
    
    @log_performance("image_upload_processing")
    async def _process_uploaded_image(self, uploaded_file) -> Dict[str, Any]:
        """Processa imagem enviada de forma assíncrona"""
        try:
            # Lê dados da imagem
            image_bytes = uploaded_file.read()
            
            # Valida tamanho do arquivo
            if len(image_bytes) > self.config.image_processing.max_file_size_mb * 1024 * 1024:
                raise ValueError(f"Arquivo muito grande. Máximo: {self.config.image_processing.max_file_size_mb}MB")
            
            # Processa imagem
            start_time = time.time()
            
            # Executa processamentos em paralelo
            tasks = [
                self.image_processor.process_image(image_bytes),
                self.color_analyzer.analyze_colors(image_bytes),
            ]
            
            image_analysis, color_analysis = await asyncio.gather(*tasks)
            
            processing_time = time.time() - start_time
            
            # Combina resultados
            result = {
                'image_analysis': image_analysis,
                'color_analysis': color_analysis,
                'processing_time': processing_time,
                'confidence': self._calculate_confidence(image_analysis, color_analysis),
                'timestamp': datetime.now().isoformat()
            }
            
            # Mantém estrutura completa para UI; validações específicas podem ser adicionadas separadamente
            return result
            
        except Exception as e:
            logger.error(f"Erro no processamento da imagem: {e}")
            raise
    
    def _calculate_confidence(self, image_analysis: Dict, color_analysis: Dict) -> float:
        """Calcula confiança geral da análise"""
        confidence_factors = []
        
        # Confiança baseada na qualidade da imagem
        if 'faces_detected' in image_analysis and image_analysis['faces_detected'] > 0:
            confidence_factors.append(0.9)
        else:
            confidence_factors.append(0.5)
        
        # Confiança baseada na análise de pele
        skin_confidence = color_analysis.get('skin_analysis', {}).get('confidence', 0)
        confidence_factors.append(skin_confidence)
        
        # Confiança baseada na harmonia de cores
        harmony_score = color_analysis.get('harmony_analysis', {}).get('harmony_score', 0)
        confidence_factors.append(harmony_score)
        
        return np.mean(confidence_factors) if confidence_factors else 0.0
    
    def run(self):
        """Executa a aplicação principal"""
        try:
            # Configura Streamlit
            self._configure_streamlit()

            # Obtém contexto da sessão
            session_context = SessionManager.get_user_context()

            with log_context(
                request_id=session_context['session_id'],
                user_agent=session_context.get('user_agent')
            ):
                # Renderiza interface
                self.ui.render_header()

                # Layout principal
                col1, col2 = st.columns([2, 3])

                with col1:
                    st.subheader("📸 Upload da Imagem")

                    uploaded_file = st.file_uploader(
                        "Escolha sua foto",
                        type=['png', 'jpg', 'jpeg', 'webp', 'bmp', 'tiff', 'tif', 'gif'],
                        help="Formatos suportados: PNG, JPG, JPEG, WEBP"
                    )

                    if uploaded_file:
                        # Mostra preview da imagem
                        image = Image.open(uploaded_file)
                        st.image(image, caption="Sua foto", width='stretch')
                        # Guarda para try-on
                        st.session_state.person_image = image

                        # Processa imagem
                        if st.button("🚀 Analisar Imagem", type="primary"):
                            with self.ui.render_loading_animation("Analisando sua coloração..."):
                                try:
                                    # Reset do arquivo para leitura
                                    uploaded_file.seek(0)

                                    # Processa de forma assíncrona usando asyncio.run
                                    result = asyncio.run(self._process_uploaded_image(uploaded_file))

                                    # Salva resultado na sessão
                                    st.session_state.analysis_result = result
                                    st.session_state.analysis_complete = True

                                    self.ui.render_success_message("Análise concluída com sucesso!")

                                    # Fase 1 TryOn: cria modelo base uma vez
                                    try:
                                        st.session_state.user_model_meta = self.tryon.create_user_model(image)
                                        st.session_state.user_model_id = st.session_state.user_model_meta.get('model_id')
                                    except Exception as e:
                                        logger.error(f"Falha ao criar modelo base do usuário: {e}")

                                except Exception as e:
                                    error_msg = "Erro durante a análise da imagem"
                                    self.ui.render_error_message(error_msg, str(e))
                                    logger.error(f"{error_msg}: {e}", extra={"user_agent": session_context.get('user_agent')})

                with col2:
                    st.subheader("📊 Resultados da Análise")

                    if st.session_state.get('analysis_complete', False):
                        result = st.session_state.get('analysis_result', {})

                        # Dashboard de métricas
                        metrics = {
                            'processing_time': result.get('processing_time', 0),
                            'confidence': result.get('confidence', 0),
                            'colors_detected': len(result.get('color_analysis', {}).get('dominant_colors', [])),
                            'image_quality': 'Alta' if result.get('confidence', 0) > 0.7 else 'Média'
                        }

                        self.ui.render_metrics_dashboard(metrics)

                        # Resultados detalhados
                        with st.expander("🎨 Análise de Cores Detalhada", expanded=True):
                            color_analysis = result.get('color_analysis', {})

                            if 'dominant_colors' in color_analysis:
                                st.write("**Cores Dominantes:**")
                                colors = color_analysis['dominant_colors']

                                # Mostra cores em grid
                                cols = st.columns(min(5, len(colors)))
                                for i, color in enumerate(colors[:5]):
                                    with cols[i]:
                                        rgb_color = f"rgb({color[2]}, {color[1]}, {color[0]})"
                                        st.markdown(f"""
                                        <div style="
                                            width: 60px;
                                            height: 60px;
                                            background-color: {rgb_color};
                                            border-radius: 50%;
                                            margin: 0 auto;
                                            border: 2px solid #ddd;
                                        "></div>
                                        <p style="text-align: center; font-size: 10px;">
                                            {rgb_color}
                                        </p>
                                        """, unsafe_allow_html=True)

                            # Análise de tom de pele
                            skin_analysis = color_analysis.get('skin_analysis', {})
                            if skin_analysis.get('skin_tone'):
                                st.write(f"**Tom de Pele:** {skin_analysis.get('subtone', 'N/A')}")
                                st.write(f"**Confiança:** {skin_analysis.get('confidence', 0):.1%}")

                        # Recomendações (paleta + relatório)
                        with st.expander("👗 Recomendações de Cores", expanded=True):
                            legacy_measures = self._build_legacy_measurements(result)
                            rec = generate_recommendations(legacy_measures)
                            if rec.colors_bgr:
                                season_label = rec.season.capitalize() if rec.season else 'Indefinida'
                                st.write(f"**Sua estação:** {season_label}")

                                cols_per_row = min(5, max(1, len(rec.colors_bgr)))
                                rows = (len(rec.colors_bgr) + cols_per_row - 1) // cols_per_row
                                for r in range(rows):
                                    row_cols = st.columns(cols_per_row)
                                    for c in range(cols_per_row):
                                        idx = r * cols_per_row + c
                                        if idx < len(rec.colors_bgr):
                                            bgr = rec.colors_bgr[idx]
                                            rgb = f"rgb({bgr[2]}, {bgr[1]}, {bgr[0]})"
                                            with row_cols[c]:
                                                st.markdown(f"""
                                                <div style="
                                                    width: 70px;
                                                    height: 70px;
                                                    background-color: {rgb};
                                                    border-radius: 8px;
                                                    border: 2px solid #ddd;
                                                    margin: 0 auto;">
                                                </div>
                                                <p style=\"text-align:center; font-size: 10px; margin: 4px 0 0 0;\">{rgb}</p>
                                                """, unsafe_allow_html=True)

                                report_data = build_text_report(rec.colors_bgr, legacy_measures)
                                st.download_button(
                                    label="📥 Baixar Relatório de Cores",
                                    data=report_data,
                                    file_name=f"color_palette_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                                    mime="text/plain"
                                )
                            else:
                                st.info("Nenhuma cor recomendada encontrada. Verifique o catálogo.")

                        # Recomendações de roupas (estilo textual)
                        with st.expander("👗 Recomendações de Estilo", expanded=True):
                            self._render_style_recommendations(result)

                        # Try-on virtual (Hugging Face)
                        with st.expander("🧪 Provar Roupa Virtualmente", expanded=False):
                            # Botão para recriar serviço (útil para problemas de autenticação)
                            col1, col2 = st.columns([3, 1])
                            with col1:
                                garment_file = st.file_uploader("Envie a imagem da roupa", type=['png', 'jpg', 'jpeg', 'webp', 'bmp', 'tiff', 'tif', 'gif'], key="garment_upload")
                            with col2:
                                if st.button("🔄 Recriar Serviço", help="Use se houver problemas de autenticação"):
                                    from huggingface_service import clear_hf_service_cache
                                    clear_hf_service_cache()
                                    st.success("Serviço recriado! Tente o try-on novamente.")
                                    st.rerun()
                            
                            if garment_file and st.session_state.get('person_image') is not None:
                                garment_img = Image.open(garment_file)
                                st.image(garment_img, caption="Roupa enviada", width='content')
                                if st.button("✨ Gerar Try-On"):
                                    with self.ui.render_loading_animation("Gerando try-on..."):
                                        try:
                                            # Fase 2 TryOn: usa modelo base + roupa
                                            if st.session_state.get('user_model_id'):
                                                output_path = asyncio.run(self.tryon.try_on(st.session_state['user_model_id'], garment_img))
                                            else:
                                                # Fallback: chamada direta
                                                output_path = asyncio.run(virtual_try_on_streamlit(st.session_state.person_image, garment_img))
                                            if output_path:
                                                logger.info(f"Try-on gerado com sucesso. Caminho: {output_path}")
                                                logger.info(f"Arquivo existe: {os.path.exists(output_path)}")
                                                logger.info(f"Tamanho do arquivo: {os.path.getsize(output_path) if os.path.exists(output_path) else 'N/A'} bytes")
                                                
                                                if os.path.exists(output_path):
                                                    st.success(f"✅ Try-on gerado com sucesso!")
                                                    st.image(output_path, caption="Resultado do Try-On", width='stretch')
                                                    
                                                    # Botão de download
                                                    try:
                                                        with open(output_path, "rb") as f:
                                                            st.download_button("Baixar imagem gerada", f, file_name="resultado_vton.png")
                                                    except Exception as download_error:
                                                        logger.error(f"Erro no botão de download: {download_error}")
                                                        st.error(f"Erro no download: {download_error}")
                                                else:
                                                    st.error(f"❌ Arquivo não encontrado: {output_path}")
                                                    st.info("Tente usar o botão '🔄 Recriar Serviço' e fazer o try-on novamente")
                                            else:
                                                logger.warning("output_path é None ou vazio")
                                                st.warning("Não foi possível gerar o try-on.")
                                        except Exception as e:
                                            self.ui.render_error_message("Erro no try-on", str(e))

                        # Botão de download do relatório
                        if st.button("📥 Baixar Relatório Completo"):
                            report_data = self._generate_report(result)
                            st.download_button(
                                label="📄 Download Relatório",
                                data=report_data,
                                file_name=f"analise_coloracao_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                                mime="application/json"
                            )

                    else:
                        st.info("👆 Faça upload de uma imagem para começar a análise")

                # Sidebar
                self.ui.render_sidebar(self.cache_service)

        except Exception as e:
            logger.error(f"Erro na execução da aplicação: {e}")
            self.ui.render_error_message("Erro interno da aplicação", str(e))

    def _convert_image_format(self, image: Image.Image, target_format: str = "PNG") -> Image.Image:
        """Converte imagem para formato compatível se necessário"""
        try:
            # Se a imagem já está no formato desejado, retorna como está
            if image.format == target_format:
                return image
            
            # Converte para RGB se necessário (para formatos como RGBA, CMYK, etc.)
            if image.mode not in ['RGB', 'L']:
                image = image.convert('RGB')
            
            # Se a imagem tem transparência, remove o fundo branco
            if image.mode == 'RGBA':
                # Cria fundo branco
                background = Image.new('RGB', image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[-1])  # Usa canal alpha como máscara
                image = background
            
            return image
            
        except Exception as e:
            logger.warning(f"Erro na conversão de formato: {e}")
            # Fallback: converte para RGB
            return image.convert('RGB')

    def _build_legacy_measurements(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """Mapeia o resultado otimizado para o dicionário esperado pelas regras CSV."""
        legacy: Dict[str, Any] = {}
        color_analysis = analysis_result.get('color_analysis', {})
        image_analysis = analysis_result.get('image_analysis', {})

        subtone = color_analysis.get('skin_analysis', {}).get('subtone')
        if isinstance(subtone, str):
            legacy['Subtom'] = subtone.capitalize()

        contrast_level = color_analysis.get('contrast_analysis', {}).get('contrast_level')
        if contrast_level == 'alto':
            legacy['Classificação'] = 'Alto contraste'
        elif contrast_level == 'médio':
            legacy['Classificação'] = 'Médio contraste'
        else:
            legacy['Classificação'] = 'Baixo contraste escuro'

        color_variance = image_analysis.get('color_variance', 0)
        if color_variance > 6000:
            legacy['Intensidade'] = 'Alta'
        elif color_variance > 3000:
            legacy['Intensidade'] = 'Média'
        else:
            legacy['Intensidade'] = 'Baixa'

        avg_brightness = image_analysis.get('average_brightness', 0)
        legacy['Profundidade'] = 'Claro' if avg_brightness and avg_brightness > 160 else 'Escuro'

        return legacy

    def _render_style_recommendations(self, analysis_result: Dict[str, Any]):
        """Renderiza recomendações de estilo baseadas na análise"""
        color_analysis = analysis_result.get('color_analysis', {})
        skin_analysis = color_analysis.get('skin_analysis', {})

        subtone = skin_analysis.get('subtone', 'neutro').lower()

        # Recomendações baseadas no subtom
        recommendations = {
            'quente': {
                'colors': ['Dourado', 'Coral', 'Pêssego', 'Terracota', 'Amarelo'],
                'avoid': ['Azul frio', 'Rosa frio', 'Prata'],
                'season': 'Outono/Primavera'
            },
            'frio': {
                'colors': ['Azul royal', 'Rosa frio', 'Roxo', 'Prata', 'Branco puro'],
                'avoid': ['Dourado', 'Laranja', 'Amarelo quente'],
                'season': 'Inverno/Verão'
            },
            'neutro': {
                'colors': ['Cinza', 'Bege', 'Azul marinho', 'Branco off', 'Verde oliva'],
                'avoid': ['Cores muito vibrantes'],
                'season': 'Todas as estações'
            }
        }

        rec = recommendations.get(subtone, recommendations['neutro'])

        st.write(f"**Sua estação:** {rec['season']}")

        col1, col2 = st.columns(2)

        with col1:
            st.write("**✅ Cores recomendadas:**")
            for color in rec['colors']:
                st.write(f"• {color}")

        with col2:
            st.write("**❌ Cores a evitar:**")
            for color in rec['avoid']:
                st.write(f"• {color}")
    
    def _generate_report(self, analysis_result: Dict[str, Any]) -> str:
        """Gera relatório completo em JSON"""
        import json
        
        report = {
            'metadata': {
                'app_version': self.config.version,
                'analysis_timestamp': analysis_result.get('timestamp'),
                'session_id': SessionManager.get_session_id(),
                'processing_time_seconds': analysis_result.get('processing_time', 0),
                'confidence_score': analysis_result.get('confidence', 0)
            },
            'analysis_results': analysis_result
        }
        
        return json.dumps(report, indent=2, ensure_ascii=False, default=str)


def main():
    """Função principal da aplicação"""
    try:
        app = VesteAIUltraOptimized()
        app.run()  # Remova o asyncio.run aqui
        
    except Exception as e:
        logger.error(f"Erro crítico na aplicação: {e}")
        st.error("❌ Erro crítico na aplicação. Verifique os logs para mais detalhes.")


if __name__ == "__main__":
    main()
