"""
Sistema de logging avançado com estruturação, métricas e monitoramento
"""
import asyncio
import json
import logging
import logging.handlers
import sys
import time
import traceback
from contextlib import contextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union
from functools import wraps
import threading

from config_manager import get_config


class StructuredFormatter(logging.Formatter):
    """Formatter que produz logs estruturados em JSON"""
    
    def format(self, record: logging.LogRecord) -> str:
        # Dados básicos do log
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Adiciona informações extras se disponíveis
        if hasattr(record, 'user_id'):
            log_data['user_id'] = record.user_id
        
        if hasattr(record, 'request_id'):
            log_data['request_id'] = record.request_id
        
        if hasattr(record, 'execution_time'):
            log_data['execution_time_ms'] = record.execution_time
        
        if hasattr(record, 'extra_data'):
            log_data['extra'] = record.extra_data
        
        # Adiciona stack trace para erros
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        return json.dumps(log_data, ensure_ascii=False, default=str)


class PerformanceLogger:
    """Logger especializado para métricas de performance"""
    
    def __init__(self, logger_name: str = "performance"):
        self.logger = logging.getLogger(logger_name)
        self.metrics = {}
        self._lock = threading.Lock()
    
    def log_execution_time(self, operation: str, execution_time: float, **kwargs):
        """Log de tempo de execução"""
        with self._lock:
            if operation not in self.metrics:
                self.metrics[operation] = {
                    'count': 0,
                    'total_time': 0,
                    'min_time': float('inf'),
                    'max_time': 0,
                    'avg_time': 0
                }
            
            metrics = self.metrics[operation]
            metrics['count'] += 1
            metrics['total_time'] += execution_time
            metrics['min_time'] = min(metrics['min_time'], execution_time)
            metrics['max_time'] = max(metrics['max_time'], execution_time)
            metrics['avg_time'] = metrics['total_time'] / metrics['count']
        
        self.logger.info(
            f"Performance metric: {operation}",
            extra={
                'perf_execution_time_ms': execution_time * 1000,  # em ms
                'perf_operation': operation,
                'perf_extra': kwargs
            }
        )
    
    def get_metrics(self) -> Dict[str, Any]:
        """Retorna métricas coletadas"""
        with self._lock:
            return {
                operation: {
                    **data,
                    'avg_time_ms': data['avg_time'] * 1000,
                    'min_time_ms': data['min_time'] * 1000,
                    'max_time_ms': data['max_time'] * 1000,
                    'total_time_ms': data['total_time'] * 1000
                }
                for operation, data in self.metrics.items()
            }
    
    def reset_metrics(self):
        """Reseta métricas coletadas"""
        with self._lock:
            self.metrics.clear()


class ErrorTracker:
    """Rastreador de erros com agregação e alertas"""
    
    def __init__(self, logger_name: str = "errors"):
        self.logger = logging.getLogger(logger_name)
        self.error_counts = {}
        self.recent_errors = []
        self.max_recent_errors = 100
        self._lock = threading.Lock()
    
    def track_error(self, error: Exception, context: Dict[str, Any] = None):
        """Rastreia um erro"""
        error_type = type(error).__name__
        error_message = str(error)
        
        with self._lock:
            # Conta erros por tipo
            if error_type not in self.error_counts:
                self.error_counts[error_type] = 0
            self.error_counts[error_type] += 1
            
            # Mantém lista de erros recentes
            error_info = {
                'timestamp': datetime.now().isoformat(),
                'type': error_type,
                'message': error_message,
                'context': context or {},
                'traceback': traceback.format_exc()
            }
            
            self.recent_errors.append(error_info)
            if len(self.recent_errors) > self.max_recent_errors:
                self.recent_errors.pop(0)
        
        # Log do erro
        self.logger.error(
            f"Error tracked: {error_type}: {error_message}",
            exc_info=True,
            extra={
                'error_type': error_type,
                'error_context': context or {}
            }
        )
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas de erros"""
        with self._lock:
            return {
                'error_counts': self.error_counts.copy(),
                'total_errors': sum(self.error_counts.values()),
                'recent_errors_count': len(self.recent_errors),
                'unique_error_types': len(self.error_counts)
            }
    
    def get_recent_errors(self, limit: int = 10) -> list:
        """Retorna erros recentes"""
        with self._lock:
            return self.recent_errors[-limit:] if self.recent_errors else []


class LoggingManager:
    """Gerenciador centralizado de logging"""
    
    def __init__(self):
        self.config = get_config()
        self.performance_logger = PerformanceLogger()
        self.error_tracker = ErrorTracker()
        self._configured = False
    
    def configure_logging(self):
        """Configura o sistema de logging"""
        if self._configured:
            return
        
        # Configuração básica
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG if self.config.debug else logging.INFO)
        
        # Remove handlers existentes
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Handler para console
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        if self.config.debug:
            # Formato simples para desenvolvimento
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        else:
            # Formato estruturado para produção
            console_formatter = StructuredFormatter()
        
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # Handler para arquivo
        if self.config.logs_dir:
            self.config.logs_dir.mkdir(parents=True, exist_ok=True)
            
            # Log geral
            file_handler = logging.handlers.RotatingFileHandler(
                self.config.logs_dir / "app.log",
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(StructuredFormatter())
            root_logger.addHandler(file_handler)
            
            # Log de erros separado
            error_handler = logging.handlers.RotatingFileHandler(
                self.config.logs_dir / "errors.log",
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=10,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(StructuredFormatter())
            root_logger.addHandler(error_handler)
            
            # Log de performance separado
            perf_handler = logging.handlers.RotatingFileHandler(
                self.config.logs_dir / "performance.log",
                maxBytes=5 * 1024 * 1024,  # 5MB
                backupCount=3,
                encoding='utf-8'
            )
            perf_handler.setLevel(logging.INFO)
            perf_handler.setFormatter(StructuredFormatter())
            
            # Adiciona handler apenas ao logger de performance
            perf_logger = logging.getLogger("performance")
            perf_logger.addHandler(perf_handler)
            perf_logger.propagate = False  # Evita duplicação
        
        self._configured = True
        logging.getLogger(__name__).info("Sistema de logging configurado")
    
    def get_logger(self, name: str) -> logging.Logger:
        """Retorna logger configurado"""
        if not self._configured:
            self.configure_logging()
        return logging.getLogger(name)


# Instância global
_logging_manager = LoggingManager()


def get_logger(name: str = None) -> logging.Logger:
    """Função de conveniência para obter logger"""
    logger_name = name or __name__
    return _logging_manager.get_logger(logger_name)


def configure_logging():
    """Configura o sistema de logging"""
    _logging_manager.configure_logging()


def log_performance(operation: str = None):
    """Decorator para log automático de performance"""
    def decorator(func):
        op_name = operation or f"{func.__module__}.{func.__name__}"
        
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    _logging_manager.performance_logger.log_execution_time(
                        op_name, execution_time, success=True
                    )
                    return result
                except Exception as e:
                    execution_time = time.time() - start_time
                    _logging_manager.performance_logger.log_execution_time(
                        op_name, execution_time, success=False, error=str(e)
                    )
                    _logging_manager.error_tracker.track_error(e, {'operation': op_name})
                    raise
            return async_wrapper
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    _logging_manager.performance_logger.log_execution_time(
                        op_name, execution_time, success=True
                    )
                    return result
                except Exception as e:
                    execution_time = time.time() - start_time
                    _logging_manager.performance_logger.log_execution_time(
                        op_name, execution_time, success=False, error=str(e)
                    )
                    _logging_manager.error_tracker.track_error(e, {'operation': op_name})
                    raise
            return sync_wrapper
    return decorator


def track_errors(context: Dict[str, Any] = None):
    """Decorator para rastreamento automático de erros"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_context = context or {}
                error_context.update({
                    'function': func.__name__,
                    'module': func.__module__,
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys())
                })
                _logging_manager.error_tracker.track_error(e, error_context)
                raise
        return wrapper
    return decorator


@contextmanager
def log_context(request_id: str = None, user_id: str = None, **extra):
    """Context manager para adicionar contexto aos logs sem sobrescrever campos existentes."""
    old_factory = logging.getLogRecordFactory()

    def record_factory(*args, **kwargs):
        record = old_factory(*args, **kwargs)
        # Define apenas campos exclusivos para evitar conflitos
        if request_id is not None and not hasattr(record, 'request_id'):
            record.request_id = request_id
        if user_id is not None and not hasattr(record, 'user_id'):
            record.user_id = user_id
        # Usa um nome de campo que não conflita
        if extra:
            if not hasattr(record, 'context_meta'):
                record.context_meta = {}
            try:
                record.context_meta.update(extra)
            except Exception:
                pass
        return record

    logging.setLogRecordFactory(record_factory)

    try:
        yield
    finally:
        logging.setLogRecordFactory(old_factory)


def get_performance_metrics() -> Dict[str, Any]:
    """Retorna métricas de performance"""
    return _logging_manager.performance_logger.get_metrics()


def get_error_stats() -> Dict[str, Any]:
    """Retorna estatísticas de erros"""
    return _logging_manager.error_tracker.get_error_stats()


def get_recent_errors(limit: int = 10) -> list:
    """Retorna erros recentes"""
    return _logging_manager.error_tracker.get_recent_errors(limit)


# Auto-configuração na importação em modo de desenvolvimento
if get_config().debug:
    configure_logging()
