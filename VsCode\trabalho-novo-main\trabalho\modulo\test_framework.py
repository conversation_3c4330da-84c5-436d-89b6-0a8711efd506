"""
Framework de testes abrangente para VesteAI
Inclui testes unitários, de integração e benchmarks de performance
"""
import asyncio
import pytest
import numpy as np
import time
import io
from pathlib import Path
from typing import Dict, Any, List
from unittest.mock import Mock, patch, AsyncMock
from PIL import Image
import cv2

# Imports dos módulos a serem testados
from config_manager import AppConfig, ConfigManager
from advanced_cache_service import AdvancedCacheService
from optimized_image_processor import OptimizedImageProcessor
from optimized_data_processor import OptimizedColorAnalyzer, DataValidator
from dependency_injection import DIContainer
from advanced_logging import get_logger, PerformanceLogger, ErrorTracker


class TestDataGenerator:
    """Gerador de dados de teste"""
    
    @staticmethod
    def create_test_image(width: int = 100, height: int = 100, color: tuple = (255, 0, 0)) -> bytes:
        """Cria imagem de teste"""
        image = Image.new('RGB', (width, height), color)
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        return buffer.getvalue()
    
    @staticmethod
    def create_face_image(width: int = 200, height: int = 200) -> bytes:
        """Cria imagem com face simulada"""
        # Cria imagem base
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Adiciona forma oval para simular rosto
        center = (width // 2, height // 2)
        axes = (width // 3, height // 2)
        cv2.ellipse(image, center, axes, 0, 0, 360, (220, 180, 140), -1)  # Tom de pele
        
        # Adiciona olhos
        eye1_center = (center[0] - 30, center[1] - 20)
        eye2_center = (center[0] + 30, center[1] - 20)
        cv2.circle(image, eye1_center, 8, (50, 50, 50), -1)
        cv2.circle(image, eye2_center, 8, (50, 50, 50), -1)
        
        # Converte para bytes
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        buffer = io.BytesIO()
        pil_image.save(buffer, format='PNG')
        return buffer.getvalue()
    
    @staticmethod
    def create_test_measurements() -> Dict[str, Any]:
        """Cria medidas de teste"""
        return {
            'altura_total': 1.75,
            'largura_ombros': 0.45,
            'largura_quadril': 0.42,
            'proporção': 0.93,
            'tom_de_pele': [180, 140, 120],
            'tom_de_cabelo': [80, 60, 40],
            'tom_de_olho': [60, 40, 20],
            'Classificação': 'médio contraste',
            'Subtom': 'quente',
            'Tipo de corpo': 'Ampulheta',
            'Formato do rosto': 'Oval'
        }


class TestConfigManager:
    """Testes para o gerenciador de configuração"""
    
    def test_config_creation(self):
        """Testa criação de configuração"""
        config = AppConfig()
        assert config.app_name == "VesteAI"
        assert config.version == "2.0.0"
        assert isinstance(config.colors, dict)
    
    def test_config_from_env(self):
        """Testa carregamento de configuração do ambiente"""
        with patch.dict('os.environ', {'DEBUG': 'true', 'DB_HOST': 'test_host'}):
            config = AppConfig.from_env()
            assert config.debug is True
            assert config.database.host == 'test_host'
    
    def test_config_manager_singleton(self):
        """Testa padrão singleton do ConfigManager"""
        manager1 = ConfigManager()
        manager2 = ConfigManager()
        assert manager1 is manager2


class TestAdvancedCacheService:
    """Testes para o serviço de cache avançado"""
    
    @pytest.fixture
    async def cache_service(self):
        """Fixture do serviço de cache"""
        service = AdvancedCacheService(redis_url=None)  # Usa apenas cache em memória
        yield service
        await service.close()
    
    @pytest.mark.asyncio
    async def test_cache_set_get(self, cache_service):
        """Testa operações básicas de cache"""
        key = "test_key"
        value = {"test": "data", "number": 42}
        
        # Set
        result = await cache_service.set(key, value)
        assert result is True
        
        # Get
        cached_value = await cache_service.get(key)
        assert cached_value == value
    
    @pytest.mark.asyncio
    async def test_cache_ttl(self, cache_service):
        """Testa TTL do cache"""
        key = "ttl_test"
        value = "test_value"
        
        await cache_service.set(key, value, ttl=1)  # 1 segundo
        
        # Deve existir imediatamente
        cached_value = await cache_service.get(key)
        assert cached_value == value
        
        # Aguarda expiração
        await asyncio.sleep(1.1)
        
        # Deve ter expirado (apenas para cache em memória)
        cached_value = await cache_service.get(key)
        # Note: Redis TTL pode não funcionar em mock, então testamos apenas a interface
    
    @pytest.mark.asyncio
    async def test_cache_delete(self, cache_service):
        """Testa remoção do cache"""
        key = "delete_test"
        value = "test_value"
        
        await cache_service.set(key, value)
        assert await cache_service.get(key) == value
        
        result = await cache_service.delete(key)
        assert result is True
        
        cached_value = await cache_service.get(key)
        assert cached_value is None
    
    @pytest.mark.asyncio
    async def test_cache_compression(self, cache_service):
        """Testa compressão de dados"""
        key = "compression_test"
        # Cria dados grandes para testar compressão
        large_data = {"data": "x" * 2000, "numbers": list(range(1000))}
        
        await cache_service.set(key, large_data)
        cached_value = await cache_service.get(key)
        
        assert cached_value == large_data


class TestOptimizedImageProcessor:
    """Testes para o processador de imagens otimizado"""
    
    @pytest.fixture
    async def image_processor(self):
        """Fixture do processador de imagens"""
        cache_service = AdvancedCacheService(redis_url=None)
        processor = OptimizedImageProcessor(cache_service)
        yield processor
        await processor.__aexit__(None, None, None)
        await cache_service.close()
    
    @pytest.mark.asyncio
    async def test_process_image_basic(self, image_processor):
        """Testa processamento básico de imagem"""
        test_image = TestDataGenerator.create_test_image()
        
        result = await image_processor.process_image(test_image)
        
        assert isinstance(result, dict)
        assert 'width' in result
        assert 'height' in result
        assert 'processing_time' in result
        assert result['width'] == 100
        assert result['height'] == 100
    
    @pytest.mark.asyncio
    async def test_optimize_image(self, image_processor):
        """Testa otimização de imagem"""
        # Cria imagem grande
        large_image = TestDataGenerator.create_test_image(2000, 2000)
        
        optimized = await image_processor.optimize_image(large_image)
        
        assert isinstance(optimized, bytes)
        assert len(optimized) < len(large_image)  # Deve ser menor
    
    @pytest.mark.asyncio
    async def test_create_thumbnail(self, image_processor):
        """Testa criação de thumbnail"""
        test_image = TestDataGenerator.create_test_image(500, 500)
        
        thumbnail = await image_processor.create_thumbnail(test_image, (100, 100))
        
        assert isinstance(thumbnail, bytes)
        # Verifica se o thumbnail é menor que a imagem original
        assert len(thumbnail) < len(test_image)
    
    @pytest.mark.asyncio
    async def test_cache_functionality(self, image_processor):
        """Testa funcionalidade de cache"""
        test_image = TestDataGenerator.create_test_image()
        
        # Primeira chamada
        start_time = time.time()
        result1 = await image_processor.process_image(test_image)
        first_call_time = time.time() - start_time
        
        # Segunda chamada (deve usar cache)
        start_time = time.time()
        result2 = await image_processor.process_image(test_image)
        second_call_time = time.time() - start_time
        
        # Resultados devem ser iguais
        assert result1['width'] == result2['width']
        assert result1['height'] == result2['height']
        
        # Segunda chamada deve ser mais rápida (cache hit)
        assert second_call_time < first_call_time


class TestOptimizedColorAnalyzer:
    """Testes para o analisador de cores otimizado"""
    
    @pytest.fixture
    async def color_analyzer(self):
        """Fixture do analisador de cores"""
        cache_service = AdvancedCacheService(redis_url=None)
        analyzer = OptimizedColorAnalyzer(cache_service)
        yield analyzer
        await analyzer.close()
        await cache_service.close()
    
    @pytest.mark.asyncio
    async def test_analyze_colors_basic(self, color_analyzer):
        """Testa análise básica de cores"""
        test_image = TestDataGenerator.create_test_image(200, 200, (255, 128, 64))
        
        result = await color_analyzer.analyze_colors(test_image)
        
        assert isinstance(result, dict)
        assert 'dominant_colors' in result
        assert 'skin_analysis' in result
        assert 'contrast_analysis' in result
        assert 'harmony_analysis' in result
    
    @pytest.mark.asyncio
    async def test_analyze_face_image(self, color_analyzer):
        """Testa análise de imagem com rosto"""
        face_image = TestDataGenerator.create_face_image()
        
        result = await color_analyzer.analyze_colors(face_image)
        
        skin_analysis = result['skin_analysis']
        assert skin_analysis['confidence'] > 0  # Deve detectar alguma pele
    
    @pytest.mark.asyncio
    async def test_create_color_palette(self, color_analyzer):
        """Testa criação de paleta de cores"""
        colors = [[255, 0, 0], [0, 255, 0], [0, 0, 255]]  # RGB
        
        palette_bytes = await color_analyzer.create_color_palette(colors)
        
        assert isinstance(palette_bytes, bytes)
        assert len(palette_bytes) > 0
        
        # Verifica se é uma imagem válida
        palette_image = Image.open(io.BytesIO(palette_bytes))
        assert palette_image.format == 'PNG'


class TestDataValidator:
    """Testes para o validador de dados"""
    
    def test_validate_image_data(self):
        """Testa validação de dados de imagem"""
        # Imagem válida
        valid_image = np.zeros((100, 100, 3), dtype=np.uint8)
        assert DataValidator.validate_image_data(valid_image) is True
        
        # Imagem inválida
        assert DataValidator.validate_image_data(None) is False
        assert DataValidator.validate_image_data(np.array([])) is False
    
    def test_validate_color_values(self):
        """Testa validação de valores de cor"""
        # Cores válidas
        valid_colors = [255, 128, 0]
        assert DataValidator.validate_color_values(valid_colors) is True
        
        # Cores inválidas
        invalid_colors = [300, -10, 128]  # Fora do range 0-255
        assert DataValidator.validate_color_values(invalid_colors) is False
    
    def test_validate_measurements(self):
        """Testa validação de medidas"""
        test_measurements = TestDataGenerator.create_test_measurements()
        
        validated = DataValidator.validate_measurements(test_measurements)
        
        assert isinstance(validated, dict)
        assert 'altura_total' in validated
        assert 'tom_de_pele' in validated
        assert validated['Subtom'] == 'quente'


class TestPerformanceBenchmarks:
    """Benchmarks de performance"""
    
    @pytest.mark.benchmark
    @pytest.mark.asyncio
    async def test_image_processing_performance(self):
        """Benchmark de processamento de imagem"""
        cache_service = AdvancedCacheService(redis_url=None)
        processor = OptimizedImageProcessor(cache_service)
        
        test_image = TestDataGenerator.create_test_image(500, 500)
        
        # Mede tempo de processamento
        start_time = time.time()
        result = await processor.process_image(test_image)
        processing_time = time.time() - start_time
        
        # Deve processar em menos de 5 segundos
        assert processing_time < 5.0
        assert result['processing_time'] < 5.0
        
        await processor.__aexit__(None, None, None)
        await cache_service.close()
    
    @pytest.mark.benchmark
    @pytest.mark.asyncio
    async def test_color_analysis_performance(self):
        """Benchmark de análise de cores"""
        cache_service = AdvancedCacheService(redis_url=None)
        analyzer = OptimizedColorAnalyzer(cache_service)
        
        face_image = TestDataGenerator.create_face_image(300, 300)
        
        # Mede tempo de análise
        start_time = time.time()
        result = await analyzer.analyze_colors(face_image)
        analysis_time = time.time() - start_time
        
        # Deve analisar em menos de 10 segundos
        assert analysis_time < 10.0
        
        await analyzer.close()
        await cache_service.close()


class TestIntegration:
    """Testes de integração"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_full_pipeline(self):
        """Testa pipeline completo de processamento"""
        # Inicializa serviços
        cache_service = AdvancedCacheService(redis_url=None)
        image_processor = OptimizedImageProcessor(cache_service)
        color_analyzer = OptimizedColorAnalyzer(cache_service)
        
        # Cria imagem de teste
        test_image = TestDataGenerator.create_face_image(400, 400)
        
        try:
            # Executa pipeline completo
            image_result = await image_processor.process_image(test_image)
            color_result = await color_analyzer.analyze_colors(test_image)
            
            # Verifica resultados
            assert isinstance(image_result, dict)
            assert isinstance(color_result, dict)
            assert 'processing_time' in image_result
            assert 'dominant_colors' in color_result
            
            # Verifica se os resultados fazem sentido
            assert image_result['width'] > 0
            assert image_result['height'] > 0
            assert len(color_result['dominant_colors']) > 0
            
        finally:
            # Cleanup
            await image_processor.__aexit__(None, None, None)
            await color_analyzer.close()
            await cache_service.close()


# Configuração do pytest
def pytest_configure(config):
    """Configuração do pytest"""
    config.addinivalue_line("markers", "benchmark: marca testes de benchmark")
    config.addinivalue_line("markers", "integration: marca testes de integração")


# Fixtures globais
@pytest.fixture(scope="session")
def event_loop():
    """Fixture do event loop para testes assíncronos"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


if __name__ == "__main__":
    # Executa testes se chamado diretamente
    pytest.main([__file__, "-v", "--tb=short"])
