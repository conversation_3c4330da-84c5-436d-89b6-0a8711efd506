"""
Processador de dados otimizado com operações vetorizadas e validação
"""
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path
import cv2
from concurrent.futures import ThreadPoolExecutor
import time

from config_manager import get_config
from dependency_injection import IColorAnalyzer, IRecommendationEngine, injectable
from advanced_logging import get_logger, log_performance, track_errors
from advanced_cache_service import AdvancedCacheService

logger = get_logger(__name__)


class DataValidator:
    """Validador de dados com regras configuráveis"""
    
    @staticmethod
    def validate_image_data(image_data: np.ndarray) -> bool:
        """Valida dados de imagem"""
        if image_data is None or image_data.size == 0:
            return False
        
        if len(image_data.shape) not in [2, 3]:
            return False
        
        if len(image_data.shape) == 3 and image_data.shape[2] not in [1, 3, 4]:
            return False
        
        return True
    
    @staticmethod
    def validate_color_values(colors: Union[List, np.ndarray]) -> bool:
        """Valida valores de cor"""
        if isinstance(colors, list):
            colors = np.array(colors)
        
        if colors.size == 0:
            return False
        
        # Verifica se os valores estão no range válido (0-255)
        return np.all((colors >= 0) & (colors <= 255))
    
    @staticmethod
    def validate_measurements(measurements: Dict[str, Any]) -> Dict[str, Any]:
        """Valida e limpa medidas extraídas"""
        validated = {}
        
        # Validações específicas
        numeric_fields = ['altura_total', 'largura_ombros', 'largura_quadril', 'proporção']
        for field in numeric_fields:
            if field in measurements:
                try:
                    value = float(measurements[field])
                    if 0 < value < 10:  # Range razoável
                        validated[field] = value
                except (ValueError, TypeError):
                    logger.warning(f"Valor inválido para {field}: {measurements[field]}")
        
        # Validação de cores
        color_fields = ['tom_de_pele', 'tom_de_cabelo', 'tom_de_olho']
        for field in color_fields:
            if field in measurements:
                color_value = measurements[field]
                if isinstance(color_value, (list, np.ndarray)) and len(color_value) == 3:
                    if DataValidator.validate_color_values(color_value):
                        validated[field] = np.array(color_value).astype(int).tolist()
        
        # Campos de texto
        text_fields = ['Classificação', 'Subtom', 'Tipo de corpo', 'Formato do rosto']
        for field in text_fields:
            if field in measurements and isinstance(measurements[field], str):
                validated[field] = measurements[field].strip()
        
        return validated


@injectable
class OptimizedColorAnalyzer(IColorAnalyzer):
    """Analisador de cores otimizado com processamento vetorizado"""
    
    def __init__(self, cache_service: AdvancedCacheService):
        self.config = get_config()
        self.cache_service = cache_service
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Carrega dados de referência uma vez
        self._load_reference_data()
        
        logger.info("OptimizedColorAnalyzer inicializado")
    
    def _load_reference_data(self):
        """Carrega dados de referência para análise de cores"""
        # Subtons de referência em formato vetorizado
        self.reference_subtones = {
            'quente': np.array([
                [255, 220, 177],  # Pêssego
                [255, 228, 196],  # Bisque
                [255, 218, 185],  # Peach Puff
            ]),
            'frio': np.array([
                [255, 240, 245],  # Lavender Blush
                [240, 248, 255],  # Alice Blue
                [248, 248, 255],  # Ghost White
            ]),
            'neutro': np.array([
                [245, 245, 220],  # Beige
                [255, 228, 225],  # Misty Rose
                [250, 240, 230],  # Linen
            ])
        }
        
        # Converte para LAB para melhor comparação
        self.reference_subtones_lab = {}
        for subtone, colors in self.reference_subtones.items():
            lab_colors = []
            for color in colors:
                # Converte RGB para LAB
                rgb_normalized = color.reshape(1, 1, 3).astype(np.uint8)
                lab = cv2.cvtColor(rgb_normalized, cv2.COLOR_RGB2LAB)
                lab_colors.append(lab.reshape(3))
            self.reference_subtones_lab[subtone] = np.array(lab_colors)
    
    @log_performance("color_analysis")
    @track_errors()
    async def analyze_colors(self, image_data: bytes) -> Dict[str, Any]:
        """Analisa cores da imagem de forma otimizada"""
        # Gera chave de cache
        cache_key = f"color_analysis_{hash(image_data)}"
        
        # Verifica cache
        cached_result = await self.cache_service.get(cache_key)
        if cached_result:
            logger.debug("Análise de cores encontrada no cache")
            return cached_result
        
        # Processa análise
        loop = asyncio.get_event_loop()
        
        # Carrega imagem
        image_array = await loop.run_in_executor(
            self.executor, self._load_image_from_bytes, image_data
        )
        
        if not DataValidator.validate_image_data(image_array):
            raise ValueError("Dados de imagem inválidos")
        
        # Executa análises em paralelo
        tasks = [
            loop.run_in_executor(self.executor, self._extract_dominant_colors, image_array),
            loop.run_in_executor(self.executor, self._analyze_skin_tone, image_array),
            loop.run_in_executor(self.executor, self._analyze_contrast, image_array),
            loop.run_in_executor(self.executor, self._calculate_color_harmony, image_array),
        ]
        
        dominant_colors, skin_analysis, contrast_analysis, harmony_analysis = await asyncio.gather(*tasks)
        
        # Combina resultados
        result = {
            'dominant_colors': dominant_colors,
            'skin_analysis': skin_analysis,
            'contrast_analysis': contrast_analysis,
            'harmony_analysis': harmony_analysis,
            'timestamp': time.time()
        }
        
        # Salva no cache
        await self.cache_service.set(cache_key, result, ttl=3600)
        
        return result
    
    def _load_image_from_bytes(self, image_data: bytes) -> np.ndarray:
        """Carrega imagem de bytes"""
        import io
        from PIL import Image
        
        pil_image = Image.open(io.BytesIO(image_data))
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        
        # Converte para numpy array
        image_array = np.array(pil_image)
        return cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
    
    def _extract_dominant_colors(self, image: np.ndarray, k: int = 5) -> List[List[int]]:
        """Extrai cores dominantes usando K-means otimizado"""
        # Redimensiona imagem para acelerar processamento
        height, width = image.shape[:2]
        if height * width > 100000:  # Se muito grande
            scale = np.sqrt(100000 / (height * width))
            new_height, new_width = int(height * scale), int(width * scale)
            image = cv2.resize(image, (new_width, new_height))
        
        # Reshape para lista de pixels
        pixels = image.reshape(-1, 3).astype(np.float32)
        
        # Remove pixels muito escuros ou muito claros (ruído)
        brightness = np.mean(pixels, axis=1)
        valid_pixels = pixels[(brightness > 20) & (brightness < 235)]
        
        if len(valid_pixels) < k:
            return pixels[:k].astype(int).tolist()
        
        # K-means clustering
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
        _, labels, centers = cv2.kmeans(valid_pixels, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
        
        # Ordena por frequência
        unique_labels, counts = np.unique(labels, return_counts=True)
        sorted_indices = np.argsort(counts)[::-1]
        
        dominant_colors = centers[sorted_indices].astype(int).tolist()
        return dominant_colors
    
    def _analyze_skin_tone(self, image: np.ndarray) -> Dict[str, Any]:
        """Analisa tom de pele usando detecção facial"""
        try:
            # Detecta faces
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            if len(faces) == 0:
                return {'skin_tone': None, 'confidence': 0.0}
            
            # Usa a maior face detectada
            largest_face = max(faces, key=lambda f: f[2] * f[3])
            x, y, w, h = largest_face
            
            # Extrai região da pele (centro da face)
            skin_region = image[y + h//4:y + 3*h//4, x + w//4:x + 3*w//4]
            
            if skin_region.size == 0:
                return {'skin_tone': None, 'confidence': 0.0}
            
            # Filtra pixels de pele usando HSV
            hsv = cv2.cvtColor(skin_region, cv2.COLOR_BGR2HSV)
            
            # Range para tons de pele
            lower_skin = np.array([0, 20, 70])
            upper_skin = np.array([20, 255, 255])
            mask = cv2.inRange(hsv, lower_skin, upper_skin)
            
            # Calcula tom médio da pele
            skin_pixels = skin_region[mask > 0]
            if len(skin_pixels) == 0:
                return {'skin_tone': None, 'confidence': 0.0}
            
            avg_skin_tone = np.mean(skin_pixels, axis=0)
            
            # Classifica subtom
            subtone = self._classify_subtone(avg_skin_tone)
            
            return {
                'skin_tone': avg_skin_tone.astype(int).tolist(),
                'subtone': subtone,
                'confidence': len(skin_pixels) / skin_region.size
            }
            
        except Exception as e:
            logger.warning(f"Erro na análise de tom de pele: {e}")
            return {'skin_tone': None, 'confidence': 0.0}
    
    def _classify_subtone(self, skin_color: np.ndarray) -> str:
        """Classifica subtom da pele usando comparação vetorizada"""
        # Converte para LAB
        rgb_color = skin_color[[2, 1, 0]]  # BGR to RGB
        rgb_normalized = rgb_color.reshape(1, 1, 3).astype(np.uint8)
        lab_color = cv2.cvtColor(rgb_normalized, cv2.COLOR_RGB2LAB).reshape(3)
        
        # Calcula distâncias para cada subtom
        min_distances = {}
        for subtone, reference_colors in self.reference_subtones_lab.items():
            distances = np.linalg.norm(reference_colors - lab_color, axis=1)
            min_distances[subtone] = np.min(distances)
        
        # Retorna subtom com menor distância
        return min(min_distances.keys(), key=lambda k: min_distances[k])
    
    def _analyze_contrast(self, image: np.ndarray) -> Dict[str, Any]:
        """Analisa contraste da imagem"""
        # Converte para escala de cinza
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Calcula estatísticas de contraste
        std_dev = np.std(gray)
        contrast_ratio = std_dev / np.mean(gray) if np.mean(gray) > 0 else 0
        
        # Classifica contraste
        if contrast_ratio < 0.3:
            contrast_level = "baixo"
        elif contrast_ratio < 0.6:
            contrast_level = "médio"
        else:
            contrast_level = "alto"
        
        return {
            'contrast_ratio': float(contrast_ratio),
            'contrast_level': contrast_level,
            'std_deviation': float(std_dev)
        }
    
    def _calculate_color_harmony(self, image: np.ndarray) -> Dict[str, Any]:
        """Calcula harmonia de cores"""
        # Extrai cores dominantes
        dominant_colors = self._extract_dominant_colors(image, k=3)
        
        if len(dominant_colors) < 2:
            return {'harmony_score': 0.0, 'harmony_type': 'unknown'}
        
        # Converte para HSV para análise de harmonia
        hsv_colors = []
        for color in dominant_colors:
            bgr_color = np.array(color).reshape(1, 1, 3).astype(np.uint8)
            hsv_color = cv2.cvtColor(bgr_color, cv2.COLOR_BGR2HSV).reshape(3)
            hsv_colors.append(hsv_color)
        
        hsv_colors = np.array(hsv_colors)
        
        # Analisa diferenças de matiz
        hue_diffs = []
        for i in range(len(hsv_colors)):
            for j in range(i + 1, len(hsv_colors)):
                diff = abs(hsv_colors[i][0] - hsv_colors[j][0])
                # Considera a natureza circular do matiz
                diff = min(diff, 180 - diff)
                hue_diffs.append(diff)
        
        avg_hue_diff = np.mean(hue_diffs) if hue_diffs else 0
        
        # Classifica tipo de harmonia
        if avg_hue_diff < 30:
            harmony_type = "monocromática"
            harmony_score = 0.9
        elif 30 <= avg_hue_diff < 60:
            harmony_type = "análoga"
            harmony_score = 0.8
        elif 150 <= avg_hue_diff <= 180:
            harmony_type = "complementar"
            harmony_score = 0.85
        else:
            harmony_type = "triádica"
            harmony_score = 0.7
        
        return {
            'harmony_score': harmony_score,
            'harmony_type': harmony_type,
            'average_hue_difference': float(avg_hue_diff)
        }
    
    async def create_color_palette(self, colors: List[List[int]]) -> bytes:
        """Cria paleta de cores visual"""
        if not colors:
            raise ValueError("Lista de cores vazia")
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor, self._create_palette_image, colors
        )
    
    def _create_palette_image(self, colors: List[List[int]]) -> bytes:
        """Cria imagem da paleta de cores"""
        import io
        from PIL import Image, ImageDraw
        
        # Configurações da paleta
        color_width = 100
        color_height = 100
        cols = min(5, len(colors))
        rows = (len(colors) + cols - 1) // cols
        
        # Cria imagem
        img_width = cols * color_width
        img_height = rows * color_height
        
        image = Image.new('RGB', (img_width, img_height), 'white')
        draw = ImageDraw.Draw(image)
        
        # Desenha cores
        for i, color in enumerate(colors):
            row = i // cols
            col = i % cols
            
            x1 = col * color_width
            y1 = row * color_height
            x2 = x1 + color_width
            y2 = y1 + color_height
            
            # Converte BGR para RGB
            rgb_color = (color[2], color[1], color[0])
            draw.rectangle([x1, y1, x2, y2], fill=rgb_color)
        
        # Converte para bytes
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        return buffer.getvalue()
    
    async def close(self):
        """Fecha recursos"""
        self.executor.shutdown(wait=True)
