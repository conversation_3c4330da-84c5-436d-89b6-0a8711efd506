"""
Sistema de injeção de dependência para melhor testabilidade e desacoplamento
"""
from typing import Dict, Any, Type, TypeVar, Callable, Optional, Protocol
from abc import ABC, abstractmethod
import inspect
import logging
from functools import wraps
from contextlib import contextmanager

logger = logging.getLogger(__name__)

T = TypeVar('T')


class Injectable(Protocol):
    """Protocol para classes que podem ser injetadas"""
    pass


class Singleton(type):
    """Metaclass para implementar padrão Singleton thread-safe"""
    _instances: Dict[Type, Any] = {}
    _lock = threading.Lock() if 'threading' in globals() else None
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            if cls._lock:
                with cls._lock:
                    if cls not in cls._instances:
                        cls._instances[cls] = super().__call__(*args, **kwargs)
            else:
                cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]


class DIContainer:
    """Container de injeção de dependência"""
    
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self._singletons: Dict[str, Any] = {}
        self._scoped: Dict[str, Any] = {}
        self._transient: Dict[str, Callable] = {}
    
    def register_singleton(self, interface: Type[T], implementation: Type[T] = None, name: str = None) -> 'DIContainer':
        """Registra um serviço como singleton"""
        key = name or interface.__name__
        impl = implementation or interface
        
        if key not in self._singletons:
            self._singletons[key] = impl
            logger.debug(f"Registrado singleton: {key} -> {impl.__name__}")
        
        return self
    
    def register_transient(self, interface: Type[T], implementation: Type[T] = None, name: str = None) -> 'DIContainer':
        """Registra um serviço como transient (nova instância a cada resolução)"""
        key = name or interface.__name__
        impl = implementation or interface
        
        self._transient[key] = impl
        logger.debug(f"Registrado transient: {key} -> {impl.__name__}")
        
        return self
    
    def register_scoped(self, interface: Type[T], implementation: Type[T] = None, name: str = None) -> 'DIContainer':
        """Registra um serviço com escopo (uma instância por escopo)"""
        key = name or interface.__name__
        impl = implementation or interface
        
        self._scoped[key] = impl
        logger.debug(f"Registrado scoped: {key} -> {impl.__name__}")
        
        return self
    
    def register_instance(self, interface: Type[T], instance: T, name: str = None) -> 'DIContainer':
        """Registra uma instância específica"""
        key = name or interface.__name__
        self._services[key] = instance
        logger.debug(f"Registrada instância: {key}")
        
        return self
    
    def register_factory(self, interface: Type[T], factory: Callable[[], T], name: str = None) -> 'DIContainer':
        """Registra uma factory function"""
        key = name or interface.__name__
        self._factories[key] = factory
        logger.debug(f"Registrada factory: {key}")
        
        return self
    
    def resolve(self, interface: Type[T], name: str = None) -> T:
        """Resolve uma dependência"""
        key = name or interface.__name__
        
        # Verifica instâncias registradas
        if key in self._services:
            return self._services[key]
        
        # Verifica factories
        if key in self._factories:
            return self._factories[key]()
        
        # Verifica singletons
        if key in self._singletons:
            if key not in self._services:
                self._services[key] = self._create_instance(self._singletons[key])
            return self._services[key]
        
        # Verifica transients
        if key in self._transient:
            return self._create_instance(self._transient[key])
        
        # Verifica scoped
        if key in self._scoped:
            # Para simplicidade, tratamos scoped como singleton por enquanto
            if key not in self._services:
                self._services[key] = self._create_instance(self._scoped[key])
            return self._services[key]
        
        # Tenta criar automaticamente se a classe for injetável
        try:
            return self._create_instance(interface)
        except Exception as e:
            raise ValueError(f"Não foi possível resolver dependência: {key}. Erro: {e}")
    
    def _create_instance(self, cls: Type[T]) -> T:
        """Cria uma instância resolvendo suas dependências"""
        try:
            # Obtém o construtor
            init_signature = inspect.signature(cls.__init__)
            
            # Resolve parâmetros do construtor
            kwargs = {}
            for param_name, param in init_signature.parameters.items():
                if param_name == 'self':
                    continue
                
                # Verifica se tem type annotation
                if param.annotation != inspect.Parameter.empty:
                    try:
                        kwargs[param_name] = self.resolve(param.annotation)
                    except ValueError:
                        # Se não conseguir resolver e tem valor padrão, usa o padrão
                        if param.default != inspect.Parameter.empty:
                            kwargs[param_name] = param.default
                        else:
                            raise
                elif param.default != inspect.Parameter.empty:
                    kwargs[param_name] = param.default
            
            return cls(**kwargs)
            
        except Exception as e:
            logger.error(f"Erro ao criar instância de {cls.__name__}: {e}")
            raise
    
    def clear(self):
        """Limpa todas as dependências registradas"""
        self._services.clear()
        self._factories.clear()
        self._singletons.clear()
        self._scoped.clear()
        self._transient.clear()
        logger.info("Container DI limpo")


# Container global
_container = DIContainer()


def get_container() -> DIContainer:
    """Retorna o container global de DI"""
    return _container


def inject(interface: Type[T] = None, name: str = None):
    """Decorator para injeção automática de dependências"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Resolve dependências baseado na assinatura da função
            sig = inspect.signature(func)
            
            for param_name, param in sig.parameters.items():
                if param_name not in kwargs and param.annotation != inspect.Parameter.empty:
                    try:
                        kwargs[param_name] = _container.resolve(param.annotation)
                    except ValueError:
                        # Se não conseguir resolver e tem valor padrão, continua
                        if param.default == inspect.Parameter.empty:
                            raise
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


def injectable(cls: Type[T]) -> Type[T]:
    """Decorator para marcar uma classe como injetável"""
    # Registra automaticamente como transient
    _container.register_transient(cls)
    return cls


def singleton(cls: Type[T]) -> Type[T]:
    """Decorator para marcar uma classe como singleton"""
    _container.register_singleton(cls)
    return cls


@contextmanager
def scope():
    """Context manager para criar um escopo de dependências"""
    # Salva estado atual
    old_scoped = _container._services.copy()
    
    try:
        yield _container
    finally:
        # Restaura estado (remove dependências scoped)
        for key in list(_container._services.keys()):
            if key not in old_scoped:
                del _container._services[key]


# Interfaces abstratas para os principais serviços
class IImageProcessor(ABC):
    """Interface para processamento de imagens"""
    
    @abstractmethod
    async def process_image(self, image_data: bytes) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    async def optimize_image(self, image_data: bytes) -> bytes:
        pass


class IColorAnalyzer(ABC):
    """Interface para análise de cores"""
    
    @abstractmethod
    async def analyze_colors(self, image_data: bytes) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    def create_color_palette(self, colors: list) -> bytes:
        pass


class IRecommendationEngine(ABC):
    """Interface para motor de recomendações"""
    
    @abstractmethod
    async def get_recommendations(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    async def get_clothing_colors(self, season: str) -> list:
        pass


class IVirtualTryOn(ABC):
    """Interface para virtual try-on"""
    
    @abstractmethod
    async def try_on(self, person_image: bytes, garment_image: bytes) -> Optional[bytes]:
        pass


class ICacheService(ABC):
    """Interface para serviço de cache"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        pass


# Função para configurar dependências padrão
def configure_dependencies():
    """Configura as dependências padrão da aplicação"""
    from config_manager import get_config
    
    config = get_config()
    
    # Registra configuração como singleton
    _container.register_instance(type(config), config)
    
    logger.info("Dependências configuradas com sucesso")


# Auto-configuração na importação
try:
    import threading
except ImportError:
    threading = None
