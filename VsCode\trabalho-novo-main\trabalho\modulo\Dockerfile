# VesteAI - Dockerfile Ultra Otimizado
# Multi-stage build para reduzir tamanho da imagem final

# Stage 1: Build dependencies
FROM python:3.11-slim as builder

# Instala dependências do sistema necessárias para build
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    pkg-config \
    libopencv-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgcc-s1 \
    && rm -rf /var/lib/apt/lists/*

# Cria ambiente virtual
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Atualiza pip e instala wheel
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# Copia requirements e instala dependências Python
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Stage 2: Runtime
FROM python:3.11-slim as runtime

# Instala apenas dependências runtime necessárias
RUN apt-get update && apt-get install -y \
    libopencv-core4.5 \
    libopencv-imgproc4.5 \
    libopencv-imgcodecs4.5 \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender1 \
    libgomp1 \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copia ambiente virtual do stage builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Cria usuário não-root para segurança
RUN groupadd -r vesteai && useradd -r -g vesteai vesteai

# Cria diretórios necessários
RUN mkdir -p /app/data /app/cache /app/logs /app/uploads \
    && chown -R vesteai:vesteai /app

# Define diretório de trabalho
WORKDIR /app

# Copia código da aplicação
COPY --chown=vesteai:vesteai . .

# Configura variáveis de ambiente
ENV PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    ENVIRONMENT=production \
    PORT=8501

# Expõe porta
EXPOSE $PORT

# Muda para usuário não-root
USER vesteai

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:$PORT/_health || exit 1

# Comando padrão
CMD ["streamlit", "run", "app_ultra_optimized.py", "--server.port=8501", "--server.address=0.0.0.0", "--server.headless=true", "--server.enableCORS=false", "--server.enableXsrfProtection=false"]
