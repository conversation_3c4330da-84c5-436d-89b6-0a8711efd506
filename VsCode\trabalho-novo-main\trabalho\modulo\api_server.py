"""
API Server FastAPI para sistema de provador virtual otimizado
Implementa endpoints /createUserModel e /tryOn
"""
import os
import asyncio
import logging
import io
from datetime import datetime
from typing import Optional, Dict, Any
import tempfile
import shutil
from pathlib import Path

from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from PIL import Image
import cv2
import numpy as np

from storage_service import get_storage_service, ArtifactType
from processing_services import get_processing_manager
from huggingface_service import get_hf_service
from config_manager import get_config

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Modelos Pydantic para validação
class CreateUserModelResponse(BaseModel):
    """Resposta do endpoint createUserModel"""
    userModelId: str
    initialImage: str  # Base64 ou URL
    processingTime: float
    artifacts: Dict[str, str]
    message: str

class TryOnRequest(BaseModel):
    """Request do endpoint tryOn"""
    userModelId: str
    clothingDescription: Optional[str] = "Roupa enviada pelo usuário"

class TryOnResponse(BaseModel):
    """Resposta do endpoint tryOn"""
    resultImage: str  # Base64 ou URL
    processingTime: float
    userModelId: str
    message: str

class ErrorResponse(BaseModel):
    """Resposta de erro padronizada"""
    error: str
    details: Optional[str] = None
    timestamp: str

# Inicializar FastAPI
app = FastAPI(
    title="VesteAI - Virtual Try-On API",
    description="API otimizada para provador virtual em duas fases",
    version="2.0.0"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Em produção, especificar domínios
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Serviços globais
storage_service = get_storage_service()
processing_manager = get_processing_manager()
hf_service = get_hf_service()

@app.on_event("startup")
async def startup_event():
    """Inicializa serviços na inicialização da API"""
    logger.info("Inicializando API VesteAI...")
    try:
        await processing_manager.initialize_all()
        logger.info("API VesteAI inicializada com sucesso")
    except Exception as e:
        logger.error(f"Erro na inicialização: {e}")
        raise

def create_error_response(error_msg: str, details: str = None) -> JSONResponse:
    """Cria resposta de erro padronizada"""
    return JSONResponse(
        status_code=400,
        content=ErrorResponse(
            error=error_msg,
            details=details,
            timestamp=datetime.now().isoformat()
        ).dict()
    )

async def validate_image(file: UploadFile) -> Image.Image:
    """Valida e converte arquivo de imagem"""
    # Verifica tipo de arquivo
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="Arquivo deve ser uma imagem")
    
    # Verifica tamanho (máximo 10MB)
    content = await file.read()
    if len(content) > 10 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="Imagem muito grande (máximo 10MB)")
    
    try:
        # Converte para PIL Image
        image = Image.open(io.BytesIO(content))
        
        # Valida dimensões mínimas
        if image.size[0] < 256 or image.size[1] < 256:
            raise HTTPException(status_code=400, detail="Imagem muito pequena (mínimo 256x256)")
        
        # Converte para RGB se necessário
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        return image
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Erro ao processar imagem: {str(e)}")

@app.post("/createUserModel", response_model=CreateUserModelResponse)
async def create_user_model(
    background_tasks: BackgroundTasks,
    user_image: UploadFile = File(..., description="Foto do usuário para criar modelo base")
):
    """
    Fase 1: Cria modelo base do usuário com processamento pesado
    
    - Extrai pose, segmentação e aplica inpainting
    - Persiste artefatos para reutilização
    - Retorna userModelId para uso posterior
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"Iniciando criação de modelo base para: {user_image.filename}")
        
        # Valida imagem
        pil_image = await validate_image(user_image)
        
        # Cria modelo base no storage
        model_id = await storage_service.create_user_model(
            pil_image, 
            user_image.filename or "user_image.png"
        )
        
        # Converte para OpenCV para processamento
        cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        # Processa imagem (operação pesada)
        processing_results = await processing_manager.process_user_image(cv_image)
        
        # Salva artefatos processados
        artifacts = {}
        
        # Salva mapa de pose
        if processing_results["pose"]["pose_map"] is not None:
            pose_path = await storage_service.save_artifact(
                model_id, ArtifactType.POSE_MAP, processing_results["pose"]["pose_map"]
            )
            artifacts["pose_map"] = str(pose_path)
        
        # Salva máscara de segmentação
        if processing_results["segmentation"]["mask"] is not None:
            mask_path = await storage_service.save_artifact(
                model_id, ArtifactType.SEGMENTATION_MASK, processing_results["segmentation"]["mask"]
            )
            artifacts["segmentation_mask"] = str(mask_path)
        
        # Salva imagem limpa (inpainting)
        if processing_results["inpainting"]["inpainted_image"] is not None:
            clean_path = await storage_service.save_artifact(
                model_id, ArtifactType.CLEAN_IMAGE, processing_results["inpainting"]["inpainted_image"]
            )
            artifacts["clean_image"] = str(clean_path)
        
        # Atualiza metadados com tempo de processamento
        metadata = await storage_service.get_metadata(model_id)
        processing_time = (datetime.now() - start_time).total_seconds()
        metadata.processing_time = processing_time
        await storage_service._save_metadata(model_id, metadata)
        
        # Prepara imagem inicial para resposta (original redimensionada)
        initial_image_path = await storage_service.load_artifact(model_id, ArtifactType.ORIGINAL)
        
        logger.info(f"Modelo base criado com sucesso: {model_id} ({processing_time:.2f}s)")
        
        return CreateUserModelResponse(
            userModelId=model_id,
            initialImage=str(initial_image_path),  # Em produção, usar URL ou base64
            processingTime=processing_time,
            artifacts=artifacts,
            message="Modelo base criado com sucesso"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao criar modelo base: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")

@app.post("/tryOn", response_model=TryOnResponse)
async def try_on(
    request: TryOnRequest,
    clothing_image: UploadFile = File(..., description="Imagem da roupa para try-on")
):
    """
    Fase 2: Aplica roupa usando modelo base (processamento leve)
    
    - Carrega modelo base existente
    - Aplica roupa usando HuggingFace API
    - Retorna resultado rapidamente
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"Iniciando try-on para modelo: {request.userModelId}")
        
        # Verifica se modelo existe
        if not await storage_service.model_exists(request.userModelId):
            raise HTTPException(status_code=404, detail="Modelo base não encontrado")
        
        # Valida imagem da roupa
        clothing_pil = await validate_image(clothing_image)
        
        # Carrega imagem original do usuário
        user_image_pil = await storage_service.load_artifact(
            request.userModelId, ArtifactType.ORIGINAL
        )
        
        # Aplica try-on usando HuggingFace
        result_path = await hf_service.try_on_clothing(
            user_image_pil,
            clothing_pil,
            garment_description=request.clothingDescription
        )
        
        if result_path is None:
            raise HTTPException(status_code=500, detail="Erro na geração do try-on")
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        logger.info(f"Try-on concluído: {request.userModelId} ({processing_time:.2f}s)")
        
        return TryOnResponse(
            resultImage=result_path,  # Em produção, usar URL ou base64
            processingTime=processing_time,
            userModelId=request.userModelId,
            message="Try-on realizado com sucesso"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro no try-on: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")

@app.get("/userModel/{model_id}")
async def get_user_model(model_id: str):
    """Retorna informações do modelo base"""
    try:
        if not await storage_service.model_exists(model_id):
            raise HTTPException(status_code=404, detail="Modelo não encontrado")
        
        metadata = await storage_service.get_metadata(model_id)
        return metadata.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao buscar modelo: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/userModel/{model_id}")
async def delete_user_model(model_id: str):
    """Remove modelo base e todos os artefatos"""
    try:
        success = await storage_service.delete_model(model_id)
        if not success:
            raise HTTPException(status_code=404, detail="Modelo não encontrado")
        
        return {"message": "Modelo removido com sucesso", "model_id": model_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao remover modelo: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/userModels")
async def list_user_models(limit: int = 50):
    """Lista modelos base disponíveis"""
    try:
        models = await storage_service.list_models(limit)
        return {
            "models": [model.to_dict() for model in models],
            "total": len(models)
        }
        
    except Exception as e:
        logger.error(f"Erro ao listar modelos: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats")
async def get_stats():
    """Retorna estatísticas do sistema"""
    try:
        storage_stats = storage_service.get_storage_stats()
        hf_stats = hf_service.get_cache_stats()
        
        return {
            "storage": storage_stats,
            "huggingface_cache": hf_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter estatísticas: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "2.0.0"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
