"""
Pipeline otimizado de processamento de imagens com cache, lazy loading e processamento assíncrono
"""
import asyncio
import hashlib
import io
import logging
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List, Union
import time

import cv2
import numpy as np
from PIL import Image, ImageFilter, ImageEnhance
import aiofiles

from config_manager import get_config
from dependency_injection import IImageProcessor, ICacheService, injectable

logger = logging.getLogger(__name__)


class ImageCache:
    """Cache otimizado para imagens com LRU e compressão"""
    
    def __init__(self, max_size_mb: int = 256):
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.cache: Dict[str, Tuple[bytes, float, int]] = {}  # key: (data, timestamp, size)
        self.total_size = 0
    
    def _evict_lru(self):
        """Remove itens menos recentemente usados"""
        if not self.cache:
            return
        
        # Ordena por timestamp (mais antigo primeiro)
        sorted_items = sorted(self.cache.items(), key=lambda x: x[1][1])
        
        # Remove 25% dos itens mais antigos
        items_to_remove = len(sorted_items) // 4
        for key, (_, _, size) in sorted_items[:items_to_remove]:
            del self.cache[key]
            self.total_size -= size
            logger.debug(f"Removido do cache: {key}")
    
    def get(self, key: str) -> Optional[bytes]:
        """Obtém item do cache"""
        if key in self.cache:
            data, _, size = self.cache[key]
            # Atualiza timestamp
            self.cache[key] = (data, time.time(), size)
            return data
        return None
    
    def set(self, key: str, data: bytes):
        """Adiciona item ao cache"""
        size = len(data)
        
        # Verifica se precisa fazer eviction
        while self.total_size + size > self.max_size_bytes and self.cache:
            self._evict_lru()
        
        # Adiciona ao cache
        self.cache[key] = (data, time.time(), size)
        self.total_size += size
        logger.debug(f"Adicionado ao cache: {key} ({size} bytes)")
    
    def clear(self):
        """Limpa o cache"""
        self.cache.clear()
        self.total_size = 0


@injectable
class OptimizedImageProcessor(IImageProcessor):
    """Processador de imagens otimizado com cache e processamento assíncrono"""
    
    def __init__(self, cache_service: ICacheService = None):
        self.config = get_config()
        self.cache = ImageCache(max_size_mb=128)
        self.cache_service = cache_service
        self.executor = ThreadPoolExecutor(max_workers=self.config.image_processing.num_workers)
        
        # Configurações de processamento
        self.max_size = self.config.image_processing.max_image_size
        self.thumbnail_size = self.config.image_processing.thumbnail_size
        self.quality = self.config.image_processing.quality
        
        logger.info("OptimizedImageProcessor inicializado")
    
    async def process_image(self, image_data: bytes) -> Dict[str, Any]:
        """Processa imagem de forma assíncrona com cache"""
        # Gera chave de cache
        cache_key = self._generate_cache_key(image_data, "process")
        
        # Verifica cache local
        cached_result = self.cache.get(cache_key)
        if cached_result:
            logger.debug("Resultado encontrado no cache local")
            return self._deserialize_result(cached_result)
        
        # Verifica cache distribuído se disponível
        if self.cache_service:
            cached_result = await self.cache_service.get(cache_key)
            if cached_result:
                logger.debug("Resultado encontrado no cache distribuído")
                return cached_result
        
        # Processa imagem
        start_time = time.time()
        result = await self._process_image_internal(image_data)
        processing_time = time.time() - start_time
        
        result['processing_time'] = processing_time
        logger.info(f"Imagem processada em {processing_time:.2f}s")
        
        # Salva no cache
        serialized_result = self._serialize_result(result)
        self.cache.set(cache_key, serialized_result)
        
        if self.cache_service:
            await self.cache_service.set(cache_key, result, ttl=3600)
        
        return result
    
    async def _process_image_internal(self, image_data: bytes) -> Dict[str, Any]:
        """Processamento interno da imagem"""
        loop = asyncio.get_event_loop()
        
        # Carrega imagem de forma assíncrona
        pil_image = await loop.run_in_executor(
            self.executor, self._load_image_from_bytes, image_data
        )
        
        # Otimiza imagem
        optimized_image = await loop.run_in_executor(
            self.executor, self._optimize_image_sync, pil_image
        )
        
        # Converte para OpenCV
        cv_image = await loop.run_in_executor(
            self.executor, self._pil_to_opencv, optimized_image
        )
        
        # Processa em paralelo diferentes análises
        tasks = [
            loop.run_in_executor(self.executor, self._extract_basic_info, optimized_image),
            loop.run_in_executor(self.executor, self._extract_color_info, cv_image),
            loop.run_in_executor(self.executor, self._extract_face_info, cv_image),
        ]
        
        basic_info, color_info, face_info = await asyncio.gather(*tasks)
        
        # Combina resultados
        result = {
            **basic_info,
            **color_info,
            **face_info,
            'optimized_image_data': await self._image_to_bytes(optimized_image)
        }
        
        return result
    
    async def optimize_image(self, image_data: bytes) -> bytes:
        """Otimiza imagem para reduzir tamanho e melhorar performance"""
        cache_key = self._generate_cache_key(image_data, "optimize")
        
        # Verifica cache
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        loop = asyncio.get_event_loop()
        
        # Carrega e otimiza
        pil_image = await loop.run_in_executor(
            self.executor, self._load_image_from_bytes, image_data
        )
        
        optimized_image = await loop.run_in_executor(
            self.executor, self._optimize_image_sync, pil_image
        )
        
        # Converte para bytes
        optimized_bytes = await self._image_to_bytes(optimized_image)
        
        # Salva no cache
        self.cache.set(cache_key, optimized_bytes)
        
        return optimized_bytes
    
    def _load_image_from_bytes(self, image_data: bytes) -> Image.Image:
        """Carrega imagem de bytes de forma síncrona"""
        try:
            return Image.open(io.BytesIO(image_data))
        except Exception as e:
            logger.error(f"Erro ao carregar imagem: {e}")
            raise ValueError(f"Formato de imagem inválido: {e}")
    
    def _optimize_image_sync(self, image: Image.Image) -> Image.Image:
        """Otimiza imagem de forma síncrona"""
        # Converte para RGB se necessário
        if image.mode not in ('RGB', 'L'):
            image = image.convert('RGB')
        
        # Redimensiona se necessário
        if image.size[0] > self.max_size[0] or image.size[1] > self.max_size[1]:
            image.thumbnail(self.max_size, Image.Resampling.LANCZOS)
        
        # Aplica filtros de otimização
        if image.size[0] * image.size[1] > 500000:  # Para imagens grandes
            # Aplica um leve blur para reduzir ruído
            image = image.filter(ImageFilter.GaussianBlur(radius=0.5))
        
        return image
    
    def _pil_to_opencv(self, pil_image: Image.Image) -> np.ndarray:
        """Converte PIL para OpenCV"""
        open_cv_image = np.array(pil_image)
        if len(open_cv_image.shape) == 3:
            return open_cv_image[:, :, ::-1].copy()  # RGB to BGR
        return open_cv_image
    
    def _extract_basic_info(self, image: Image.Image) -> Dict[str, Any]:
        """Extrai informações básicas da imagem"""
        return {
            'width': image.size[0],
            'height': image.size[1],
            'mode': image.mode,
            'format': image.format,
            'aspect_ratio': image.size[0] / image.size[1]
        }
    
    def _extract_color_info(self, cv_image: np.ndarray) -> Dict[str, Any]:
        """Extrai informações de cor da imagem"""
        # Calcula histograma de cores
        hist_b = cv2.calcHist([cv_image], [0], None, [256], [0, 256])
        hist_g = cv2.calcHist([cv_image], [1], None, [256], [0, 256])
        hist_r = cv2.calcHist([cv_image], [2], None, [256], [0, 256])
        
        # Calcula cor dominante
        dominant_color = np.mean(cv_image.reshape(-1, 3), axis=0)
        
        # Calcula brilho médio
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
        brightness = np.mean(gray)
        
        return {
            'dominant_color_bgr': dominant_color.astype(int).tolist(),
            'average_brightness': float(brightness),
            'color_variance': float(np.var(cv_image)),
        }
    
    def _extract_face_info(self, cv_image: np.ndarray) -> Dict[str, Any]:
        """Extrai informações faciais básicas"""
        try:
            # Carrega classificador de face (se disponível)
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            return {
                'faces_detected': len(faces),
                'face_regions': faces.tolist() if len(faces) > 0 else []
            }
        except Exception as e:
            logger.warning(f"Erro na detecção facial: {e}")
            return {
                'faces_detected': 0,
                'face_regions': []
            }
    
    async def _image_to_bytes(self, image: Image.Image) -> bytes:
        """Converte imagem PIL para bytes de forma assíncrona"""
        loop = asyncio.get_event_loop()
        
        def _convert():
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=self.quality, optimize=True)
            return buffer.getvalue()
        
        return await loop.run_in_executor(self.executor, _convert)
    
    def _generate_cache_key(self, data: bytes, operation: str) -> str:
        """Gera chave de cache baseada no hash dos dados"""
        hash_obj = hashlib.md5()
        hash_obj.update(data)
        hash_obj.update(operation.encode())
        return f"img_{operation}_{hash_obj.hexdigest()}"
    
    def _serialize_result(self, result: Dict[str, Any]) -> bytes:
        """Serializa resultado para cache"""
        import pickle
        return pickle.dumps(result)
    
    def _deserialize_result(self, data: bytes) -> Dict[str, Any]:
        """Deserializa resultado do cache"""
        import pickle
        return pickle.loads(data)
    
    async def create_thumbnail(self, image_data: bytes, size: Tuple[int, int] = None) -> bytes:
        """Cria thumbnail da imagem"""
        size = size or self.thumbnail_size
        cache_key = self._generate_cache_key(image_data + str(size).encode(), "thumbnail")
        
        # Verifica cache
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        loop = asyncio.get_event_loop()
        
        # Carrega imagem
        pil_image = await loop.run_in_executor(
            self.executor, self._load_image_from_bytes, image_data
        )
        
        # Cria thumbnail
        def _create_thumbnail():
            pil_image.thumbnail(size, Image.Resampling.LANCZOS)
            return pil_image
        
        thumbnail = await loop.run_in_executor(self.executor, _create_thumbnail)
        
        # Converte para bytes
        thumbnail_bytes = await self._image_to_bytes(thumbnail)
        
        # Salva no cache
        self.cache.set(cache_key, thumbnail_bytes)
        
        return thumbnail_bytes
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do cache"""
        return {
            'cache_size_mb': self.cache.total_size / (1024 * 1024),
            'cache_items': len(self.cache.cache),
            'max_size_mb': self.cache.max_size_bytes / (1024 * 1024)
        }
    
    def clear_cache(self):
        """Limpa o cache"""
        self.cache.clear()
        logger.info("Cache de imagens limpo")
    
    async def __aenter__(self):
        """Context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.executor.shutdown(wait=True)
