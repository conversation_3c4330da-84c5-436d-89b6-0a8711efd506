"""
Sistema de configuração centralizado e otimizado
"""
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from functools import lru_cache
import json
import logging

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """Configurações de banco de dados"""
    host: str = "localhost"
    port: int = 5432
    database: str = "vesteai"
    username: str = "postgres"
    password: str = ""
    pool_size: int = 10
    max_overflow: int = 20


@dataclass
class CacheConfig:
    """Configurações de cache"""
    redis_url: str = "redis://localhost:6379"
    default_ttl: int = 3600  # 1 hora
    max_memory: str = "256mb"
    eviction_policy: str = "allkeys-lru"


@dataclass
class ImageProcessingConfig:
    """Configurações de processamento de imagem"""
    max_image_size: tuple = (1024, 1024)
    thumbnail_size: tuple = (512, 512)
    quality: int = 85
    format: str = "JPEG"
    max_file_size_mb: int = 10
    allowed_formats: tuple = ("JPEG", "PNG", "WEBP")
    
    # Configurações de performance
    use_gpu: bool = False
    batch_size: int = 4
    num_workers: int = 4


@dataclass
class HuggingFaceConfig:
    """Configurações do Hugging Face"""
    model_name: str = "yisol/IDM-VTON"
    api_token: Optional[str] = None
    timeout: int = 300
    max_retries: int = 3
    cache_dir: str = "cache/hf"
    
    # Configurações de performance
    denoise_steps: int = 20
    guidance_scale: float = 2.5
    use_fp16: bool = True


@dataclass
class AppConfig:
    """Configuração principal da aplicação"""
    # Informações básicas
    app_name: str = "VesteAI"
    version: str = "2.0.0"
    debug: bool = False
    environment: str = "development"
    
    # Diretórios
    base_dir: Path = field(default_factory=lambda: Path(__file__).parent.parent)
    data_dir: Path = field(init=False)
    cache_dir: Path = field(init=False)
    logs_dir: Path = field(init=False)
    uploads_dir: Path = field(init=False)
    
    # Configurações de sub-sistemas
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    image_processing: ImageProcessingConfig = field(default_factory=ImageProcessingConfig)
    huggingface: HuggingFaceConfig = field(default_factory=HuggingFaceConfig)
    
    # Configurações de UI
    colors: Dict[str, str] = field(default_factory=lambda: {
        'background': '#FFF5E1',
        'sidebar': '#FFDFD3',
        'primary_text': '#5D4037',
        'secondary_text': '#795548',
        'button': '#5D4037',
        'button_hover': '#4E342E'
    })
    
    # Configurações de performance
    max_concurrent_requests: int = 10
    request_timeout: int = 30
    enable_metrics: bool = True
    enable_profiling: bool = False
    
    def __post_init__(self):
        """Inicializa diretórios derivados"""
        self.data_dir = self.base_dir / "data"
        self.cache_dir = self.base_dir / "cache"
        self.logs_dir = self.base_dir / "logs"
        self.uploads_dir = self.base_dir / "uploads"
        
        # Cria diretórios se não existirem
        for directory in [self.data_dir, self.cache_dir, self.logs_dir, self.uploads_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def from_env(cls) -> 'AppConfig':
        """Cria configuração a partir de variáveis de ambiente"""
        config = cls()
        
        # Configurações básicas
        config.debug = os.getenv('DEBUG', 'false').lower() == 'true'
        config.environment = os.getenv('ENVIRONMENT', 'development')
        
        # Database
        if db_url := os.getenv('DATABASE_URL'):
            # Parse DATABASE_URL se fornecida
            pass  # Implementar parsing se necessário
        
        config.database.host = os.getenv('DB_HOST', config.database.host)
        config.database.port = int(os.getenv('DB_PORT', config.database.port))
        config.database.database = os.getenv('DB_NAME', config.database.database)
        config.database.username = os.getenv('DB_USER', config.database.username)
        config.database.password = os.getenv('DB_PASSWORD', config.database.password)
        
        # Cache
        config.cache.redis_url = os.getenv('REDIS_URL', config.cache.redis_url)
        
        # HuggingFace
        config.huggingface.api_token = os.getenv('HF_TOKEN')
        config.huggingface.model_name = os.getenv('HF_MODEL', config.huggingface.model_name)
        
        # Performance
        config.max_concurrent_requests = int(os.getenv('MAX_CONCURRENT_REQUESTS', config.max_concurrent_requests))
        config.image_processing.use_gpu = os.getenv('USE_GPU', 'false').lower() == 'true'
        
        return config
    
    @classmethod
    def from_file(cls, config_path: Path) -> 'AppConfig':
        """Carrega configuração de arquivo JSON"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Deserialização simples (deep update dos campos usados)
            config = cls()
            
            # Campos básicos
            config.app_name = data.get('app_name', config.app_name)
            config.version = data.get('version', config.version)
            config.debug = data.get('debug', config.debug)
            config.environment = data.get('environment', config.environment)
            
            # Subconfigurações presentes
            if isinstance(data.get('huggingface'), dict):
                hf = data['huggingface']
                config.huggingface.model_name = hf.get('model_name', config.huggingface.model_name)
                config.huggingface.api_token = hf.get('api_token', config.huggingface.api_token)
                config.huggingface.timeout = hf.get('timeout', config.huggingface.timeout)
                config.huggingface.max_retries = hf.get('max_retries', config.huggingface.max_retries)
                config.huggingface.cache_dir = hf.get('cache_dir', config.huggingface.cache_dir)
                config.huggingface.denoise_steps = hf.get('denoise_steps', config.huggingface.denoise_steps)
                config.huggingface.guidance_scale = hf.get('guidance_scale', config.huggingface.guidance_scale)
                config.huggingface.use_fp16 = hf.get('use_fp16', config.huggingface.use_fp16)
            
            if isinstance(data.get('cache'), dict):
                cc = data['cache']
                config.cache.redis_url = cc.get('redis_url', config.cache.redis_url)
                config.cache.default_ttl = cc.get('default_ttl', config.cache.default_ttl)
                config.cache.max_memory = cc.get('max_memory', config.cache.max_memory)
                config.cache.eviction_policy = cc.get('eviction_policy', config.cache.eviction_policy)
            
            if isinstance(data.get('image_processing'), dict):
                ip = data['image_processing']
                config.image_processing.max_image_size = tuple(ip.get('max_image_size', config.image_processing.max_image_size))
                config.image_processing.thumbnail_size = tuple(ip.get('thumbnail_size', config.image_processing.thumbnail_size))
                config.image_processing.quality = ip.get('quality', config.image_processing.quality)
                config.image_processing.format = ip.get('format', config.image_processing.format)
                config.image_processing.max_file_size_mb = ip.get('max_file_size_mb', config.image_processing.max_file_size_mb)
                config.image_processing.allowed_formats = tuple(ip.get('allowed_formats', config.image_processing.allowed_formats))
                config.image_processing.use_gpu = ip.get('use_gpu', config.image_processing.use_gpu)
                config.image_processing.batch_size = ip.get('batch_size', config.image_processing.batch_size)
                config.image_processing.num_workers = ip.get('num_workers', config.image_processing.num_workers)
            
            return config
            
        except Exception as e:
            logger.error(f"Erro ao carregar configuração de {config_path}: {e}")
            return cls()
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte configuração para dicionário"""
        return {
            'app_name': self.app_name,
            'version': self.version,
            'debug': self.debug,
            'environment': self.environment,
            'database': {
                'host': self.database.host,
                'port': self.database.port,
                'database': self.database.database,
                'pool_size': self.database.pool_size
            },
            'cache': {
                'redis_url': self.cache.redis_url,
                'default_ttl': self.cache.default_ttl
            },
            'image_processing': {
                'max_image_size': self.image_processing.max_image_size,
                'use_gpu': self.image_processing.use_gpu,
                'batch_size': self.image_processing.batch_size
            },
            'huggingface': {
                'model_name': self.huggingface.model_name,
                'timeout': self.huggingface.timeout,
                'use_fp16': self.huggingface.use_fp16
            }
        }


class ConfigManager:
    """Gerenciador centralizado de configurações"""
    
    _instance: Optional['ConfigManager'] = None
    _config: Optional[AppConfig] = None
    
    def __new__(cls) -> 'ConfigManager':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._config is None:
            self._load_config()
    
    def _load_config(self):
        """Carrega configuração com precedência: arquivo -> env -> padrão"""
        # Procura config.json na raiz do projeto (acima da pasta modulo/)
        config_file = Path(__file__).parent.parent / "config.json"
        
        if config_file.exists():
            logger.info(f"Carregando configuração de {config_file}")
            self._config = AppConfig.from_file(config_file)
        else:
            logger.info("Arquivo config.json não encontrado, carregando configuração de variáveis de ambiente")
            self._config = AppConfig.from_env()
        
        logger.info(f"Configuração carregada: {self._config.environment}")
        if self._config.huggingface.api_token:
            logger.info(f"Token Hugging Face configurado: {self._config.huggingface.api_token[:8]}...")
        else:
            logger.warning("Token Hugging Face não configurado - operando como usuário anônimo")
    
    @property
    def config(self) -> AppConfig:
        """Retorna configuração atual"""
        if self._config is None:
            self._load_config()
        return self._config
    
    def reload(self):
        """Recarrega configuração"""
        self._config = None
        self._load_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """Obtém valor de configuração usando notação de ponto"""
        try:
            obj = self.config
            for part in key.split('.'):
                obj = getattr(obj, part)
            return obj
        except AttributeError:
            return default


# Instância global do gerenciador
@lru_cache(maxsize=1)
def get_config_manager() -> ConfigManager:
    """Retorna instância singleton do gerenciador de configuração"""
    return ConfigManager()


def get_config() -> AppConfig:
    """Função de conveniência para obter configuração"""
    return get_config_manager().config
