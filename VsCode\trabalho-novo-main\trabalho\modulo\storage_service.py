"""
Sistema de armazenamento otimizado para modelos base de usuários
Suporta persistência em disco com metadados JSON e cache em memória
"""
import os
import json
import uuid
import shutil
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import asyncio
import aiofiles
from PIL import Image
import numpy as np
import cv2
import logging
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class ArtifactType(Enum):
    """Tipos de artefatos do modelo base"""
    ORIGINAL = "original.png"
    POSE_MAP = "pose_map.png"
    SEGMENTATION_MASK = "segmentation_mask.png"
    CLEAN_IMAGE = "clean_image.png"
    EMBEDDINGS = "embeddings.npy"
    METADATA = "metadata.json"

@dataclass
class UserModelMetadata:
    """Metadados do modelo base do usuário"""
    model_id: str
    created_at: datetime
    updated_at: datetime
    original_filename: str
    image_dimensions: tuple
    processing_time: float
    artifacts: Dict[str, str]
    version: str = "1.0"
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserModelMetadata':
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        return cls(**data)

class StorageService:
    """Serviço de armazenamento para modelos base de usuários"""
    
    def __init__(self, base_dir: Optional[Path] = None, cache_size: int = 100):
        self.base_dir = base_dir or Path("cache/user_models")
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache em memória para metadados (LRU)
        self._metadata_cache: Dict[str, UserModelMetadata] = {}
        self._cache_order: List[str] = []
        self._max_cache_size = cache_size
        
        # Lock para operações thread-safe
        self._lock = asyncio.Lock()
        
        logger.info(f"StorageService inicializado: {self.base_dir}")
    
    def _generate_model_id(self) -> str:
        """Gera ID único para o modelo"""
        return f"user_model_{uuid.uuid4().hex[:12]}"
    
    def _get_model_path(self, model_id: str) -> Path:
        """Retorna caminho do diretório do modelo"""
        return self.base_dir / model_id
    
    def _update_cache(self, model_id: str, metadata: UserModelMetadata):
        """Atualiza cache LRU de metadados"""
        if model_id in self._metadata_cache:
            self._cache_order.remove(model_id)
        elif len(self._metadata_cache) >= self._max_cache_size:
            # Remove o mais antigo
            oldest = self._cache_order.pop(0)
            del self._metadata_cache[oldest]
        
        self._metadata_cache[model_id] = metadata
        self._cache_order.append(model_id)
    
    async def create_user_model(self, original_image: Image.Image, 
                              original_filename: str = "user_image.png") -> str:
        """
        Cria novo modelo base do usuário
        
        Args:
            original_image: Imagem original do usuário
            original_filename: Nome do arquivo original
            
        Returns:
            model_id: ID único do modelo criado
        """
        async with self._lock:
            model_id = self._generate_model_id()
            model_path = self._get_model_path(model_id)
            model_path.mkdir(parents=True, exist_ok=True)
            
            start_time = datetime.now()
            
            try:
                # Salva imagem original
                original_path = model_path / ArtifactType.ORIGINAL.value
                await asyncio.get_event_loop().run_in_executor(
                    None, original_image.save, str(original_path), "PNG"
                )
                
                # Cria metadados
                metadata = UserModelMetadata(
                    model_id=model_id,
                    created_at=start_time,
                    updated_at=start_time,
                    original_filename=original_filename,
                    image_dimensions=original_image.size,
                    processing_time=0.0,  # Será atualizado após processamento
                    artifacts={
                        ArtifactType.ORIGINAL.name: str(original_path)
                    }
                )
                
                # Salva metadados
                await self._save_metadata(model_id, metadata)
                
                # Atualiza cache
                self._update_cache(model_id, metadata)
                
                logger.info(f"Modelo base criado: {model_id}")
                return model_id
                
            except Exception as e:
                # Cleanup em caso de erro
                if model_path.exists():
                    shutil.rmtree(model_path)
                logger.error(f"Erro ao criar modelo base: {e}")
                raise
    
    async def save_artifact(self, model_id: str, artifact_type: ArtifactType, 
                          data: Any) -> Path:
        """
        Salva artefato do modelo base
        
        Args:
            model_id: ID do modelo
            artifact_type: Tipo do artefato
            data: Dados a serem salvos (Image, np.ndarray, etc.)
            
        Returns:
            Path: Caminho do arquivo salvo
        """
        if not await self.model_exists(model_id):
            raise ValueError(f"Modelo {model_id} não encontrado")
        
        model_path = self._get_model_path(model_id)
        artifact_path = model_path / artifact_type.value
        
        try:
            if isinstance(data, Image.Image):
                await asyncio.get_event_loop().run_in_executor(
                    None, data.save, str(artifact_path), "PNG"
                )
            elif isinstance(data, np.ndarray):
                if artifact_type == ArtifactType.EMBEDDINGS:
                    await asyncio.get_event_loop().run_in_executor(
                        None, np.save, str(artifact_path), data
                    )
                else:
                    # Assume que é imagem (OpenCV)
                    await asyncio.get_event_loop().run_in_executor(
                        None, cv2.imwrite, str(artifact_path), data
                    )
            else:
                raise ValueError(f"Tipo de dados não suportado: {type(data)}")
            
            # Atualiza metadados
            metadata = await self.get_metadata(model_id)
            metadata.artifacts[artifact_type.name] = str(artifact_path)
            metadata.updated_at = datetime.now()
            await self._save_metadata(model_id, metadata)
            
            logger.debug(f"Artefato salvo: {model_id}/{artifact_type.value}")
            return artifact_path
            
        except Exception as e:
            logger.error(f"Erro ao salvar artefato {artifact_type.value}: {e}")
            raise
    
    async def load_artifact(self, model_id: str, artifact_type: ArtifactType) -> Any:
        """
        Carrega artefato do modelo base
        
        Args:
            model_id: ID do modelo
            artifact_type: Tipo do artefato
            
        Returns:
            Dados carregados (Image, np.ndarray, etc.)
        """
        if not await self.model_exists(model_id):
            raise ValueError(f"Modelo {model_id} não encontrado")
        
        metadata = await self.get_metadata(model_id)
        if artifact_type.name not in metadata.artifacts:
            raise ValueError(f"Artefato {artifact_type.value} não encontrado")
        
        artifact_path = Path(metadata.artifacts[artifact_type.name])
        
        if not artifact_path.exists():
            raise FileNotFoundError(f"Arquivo não encontrado: {artifact_path}")
        
        try:
            if artifact_type == ArtifactType.EMBEDDINGS:
                return await asyncio.get_event_loop().run_in_executor(
                    None, np.load, str(artifact_path)
                )
            elif artifact_type in [ArtifactType.ORIGINAL]:
                return await asyncio.get_event_loop().run_in_executor(
                    None, Image.open, str(artifact_path)
                )
            else:
                # Assume que é imagem OpenCV
                return await asyncio.get_event_loop().run_in_executor(
                    None, cv2.imread, str(artifact_path)
                )
                
        except Exception as e:
            logger.error(f"Erro ao carregar artefato {artifact_type.value}: {e}")
            raise
    
    async def model_exists(self, model_id: str) -> bool:
        """Verifica se modelo existe"""
        return self._get_model_path(model_id).exists()
    
    async def get_metadata(self, model_id: str) -> UserModelMetadata:
        """Carrega metadados do modelo (com cache)"""
        # Verifica cache primeiro
        if model_id in self._metadata_cache:
            return self._metadata_cache[model_id]
        
        # Carrega do disco
        metadata_path = self._get_model_path(model_id) / ArtifactType.METADATA.value
        if not metadata_path.exists():
            raise ValueError(f"Metadados não encontrados para modelo {model_id}")
        
        async with aiofiles.open(metadata_path, 'r', encoding='utf-8') as f:
            data = json.loads(await f.read())
            metadata = UserModelMetadata.from_dict(data)
            
        # Atualiza cache
        self._update_cache(model_id, metadata)
        return metadata
    
    async def _save_metadata(self, model_id: str, metadata: UserModelMetadata):
        """Salva metadados no disco"""
        metadata_path = self._get_model_path(model_id) / ArtifactType.METADATA.value
        
        async with aiofiles.open(metadata_path, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(metadata.to_dict(), indent=2, ensure_ascii=False))
    
    async def delete_model(self, model_id: str) -> bool:
        """Remove modelo e todos os artefatos"""
        if not await self.model_exists(model_id):
            return False
        
        try:
            model_path = self._get_model_path(model_id)
            await asyncio.get_event_loop().run_in_executor(
                None, shutil.rmtree, str(model_path)
            )
            
            # Remove do cache
            if model_id in self._metadata_cache:
                del self._metadata_cache[model_id]
                self._cache_order.remove(model_id)
            
            logger.info(f"Modelo removido: {model_id}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao remover modelo {model_id}: {e}")
            return False
    
    async def list_models(self, limit: int = 100) -> List[UserModelMetadata]:
        """Lista modelos disponíveis"""
        models = []
        
        for model_dir in self.base_dir.iterdir():
            if model_dir.is_dir() and len(models) < limit:
                try:
                    metadata = await self.get_metadata(model_dir.name)
                    models.append(metadata)
                except Exception as e:
                    logger.warning(f"Erro ao carregar modelo {model_dir.name}: {e}")
        
        # Ordena por data de criação (mais recente primeiro)
        models.sort(key=lambda x: x.created_at, reverse=True)
        return models
    
    async def cleanup_old_models(self, max_age_days: int = 30) -> int:
        """Remove modelos antigos"""
        cutoff_date = datetime.now() - timedelta(days=max_age_days)
        removed_count = 0
        
        for model_dir in self.base_dir.iterdir():
            if model_dir.is_dir():
                try:
                    metadata = await self.get_metadata(model_dir.name)
                    if metadata.created_at < cutoff_date:
                        if await self.delete_model(model_dir.name):
                            removed_count += 1
                except Exception as e:
                    logger.warning(f"Erro ao verificar modelo {model_dir.name}: {e}")
        
        logger.info(f"Limpeza concluída: {removed_count} modelos removidos")
        return removed_count
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas de armazenamento"""
        total_size = 0
        model_count = 0
        
        for model_dir in self.base_dir.iterdir():
            if model_dir.is_dir():
                model_count += 1
                for file_path in model_dir.rglob("*"):
                    if file_path.is_file():
                        total_size += file_path.stat().st_size
        
        return {
            "total_models": model_count,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "cache_size": len(self._metadata_cache),
            "base_dir": str(self.base_dir)
        }

# Instância global do serviço
_storage_service: Optional[StorageService] = None

def get_storage_service() -> StorageService:
    """Retorna instância singleton do serviço de armazenamento"""
    global _storage_service
    if _storage_service is None:
        _storage_service = StorageService()
    return _storage_service
