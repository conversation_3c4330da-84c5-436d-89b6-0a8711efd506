{"timestamp": "2025-09-02T15:18:27.927609", "level": "ERROR", "logger": "__main__", "message": "<PERSON>rro crítico na aplicação: a coroutine was expected, got None", "module": "app_ultra_optimized", "function": "main", "line": 536}
{"timestamp": "2025-09-02T15:21:23.706040", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 319, "request_id": "4e2b4e3a-48cd-4f8b-8165-502a5d2f08f2", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T15:21:23.717499", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 394, "request_id": "4e2b4e3a-48cd-4f8b-8165-502a5d2f08f2", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T15:21:25.017276", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 319, "request_id": "4e2b4e3a-48cd-4f8b-8165-502a5d2f08f2", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T15:21:25.017276", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 394, "request_id": "4e2b4e3a-48cd-4f8b-8165-502a5d2f08f2", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T15:23:55.388079", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 319, "request_id": "4e2b4e3a-48cd-4f8b-8165-502a5d2f08f2", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T15:23:55.390079", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 394, "request_id": "4e2b4e3a-48cd-4f8b-8165-502a5d2f08f2", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T15:25:08.966854", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 319, "request_id": "4e2b4e3a-48cd-4f8b-8165-502a5d2f08f2", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T15:25:08.969854", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 394, "request_id": "4e2b4e3a-48cd-4f8b-8165-502a5d2f08f2", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T15:25:37.049217", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 319, "request_id": "e0ddea9f-de92-410c-bf3f-b50b243773d1", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T15:25:37.049217", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 394, "request_id": "e0ddea9f-de92-410c-bf3f-b50b243773d1", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:08:41.223509", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 319, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:08:41.223509", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 394, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:08:41.359051", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 319, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:08:41.361048", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 394, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:21:16.551271", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 319, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:21:16.551271", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 394, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:21:18.090557", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 319, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:21:18.090557", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 394, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:21:20.369334", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 319, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:21:20.369334", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 394, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:21:20.922692", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 319, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:21:20.922692", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 394, "request_id": "4e14da9e-5692-4f3c-a69c-f1a2a630d743", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:52:28.364123", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 321, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:52:28.367123", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 398, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:52:32.699977", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 321, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:52:32.701977", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 398, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:52:58.344673", "level": "ERROR", "logger": "__main__", "message": "Erro no processamento da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "_process_uploaded_image", "line": 321, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:52:58.349674", "level": "ERROR", "logger": "__main__", "message": "Erro durante a análise da imagem: \"Attempt to overwrite 'extra_data' in LogRecord\"", "module": "app_ultra_optimized", "function": "run", "line": 398, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950", "extra": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"}}
{"timestamp": "2025-09-02T16:56:39.652934", "level": "ERROR", "logger": "__main__", "message": "Erro na execução da aplicação: Invalid width value: None. Width must be either an integer (pixels), 'stretch', or 'content'.", "module": "app_ultra_optimized", "function": "run", "line": 527}
{"timestamp": "2025-09-02T16:56:44.742350", "level": "ERROR", "logger": "__main__", "message": "Erro na execução da aplicação: Invalid width value: None. Width must be either an integer (pixels), 'stretch', or 'content'.", "module": "app_ultra_optimized", "function": "run", "line": 527}
{"timestamp": "2025-09-02T16:56:51.950062", "level": "ERROR", "logger": "__main__", "message": "Erro na execução da aplicação: Invalid width value: None. Width must be either an integer (pixels), 'stretch', or 'content'.", "module": "app_ultra_optimized", "function": "run", "line": 527}
{"timestamp": "2025-09-02T16:56:59.540442", "level": "ERROR", "logger": "__main__", "message": "Erro na execução da aplicação: Invalid width value: None. Width must be either an integer (pixels), 'stretch', or 'content'.", "module": "app_ultra_optimized", "function": "run", "line": 527}
{"timestamp": "2025-09-02T16:57:25.013870", "level": "ERROR", "logger": "__main__", "message": "Erro na execução da aplicação: Invalid width value: None. Width must be either an integer (pixels), 'stretch', or 'content'.", "module": "app_ultra_optimized", "function": "run", "line": 527}
{"timestamp": "2025-09-02T16:59:06.657647", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro na serialização: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "_serialize_value", "line": 201, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950"}
{"timestamp": "2025-09-02T16:59:06.658647", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro ao definir chave img_process_b9c42823c5f78988048e47f8fdb3ea1e: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "set", "line": 282, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950"}
{"timestamp": "2025-09-02T17:04:04.814576", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on: can't concat str to bytes", "module": "huggingface_service", "function": "try_on_clothing", "line": 198, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950"}
{"timestamp": "2025-09-02T17:04:31.224550", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on: can't concat str to bytes", "module": "huggingface_service", "function": "try_on_clothing", "line": 198, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950"}
{"timestamp": "2025-09-02T17:06:01.595598", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on: can't concat str to bytes", "module": "huggingface_service", "function": "try_on_clothing", "line": 198, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950"}
{"timestamp": "2025-09-02T17:06:13.931195", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on: can't concat str to bytes", "module": "huggingface_service", "function": "try_on_clothing", "line": 198, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950"}
{"timestamp": "2025-09-02T17:09:58.979613", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on: can't concat str to bytes", "module": "huggingface_service", "function": "try_on_clothing", "line": 198, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950"}
{"timestamp": "2025-09-02T17:10:20.475142", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on: can't concat str to bytes", "module": "huggingface_service", "function": "try_on_clothing", "line": 198, "request_id": "ea0c65b6-5749-415a-9937-6e8a2a412950"}
{"timestamp": "2025-09-02T17:17:32.565852", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro na serialização: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "_serialize_value", "line": 201, "request_id": "0cad8507-0151-4404-be54-3a7c6a056b60"}
{"timestamp": "2025-09-02T17:17:32.566858", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro ao definir chave img_process_b9c42823c5f78988048e47f8fdb3ea1e: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "set", "line": 282, "request_id": "0cad8507-0151-4404-be54-3a7c6a056b60"}
{"timestamp": "2025-09-02T17:21:47.204155", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 204, "request_id": "0cad8507-0151-4404-be54-3a7c6a056b60", "exception": {"type": "AppError", "message": "Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 165, in try_on_clothing\n    result = await asyncio.wait_for(\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 168, in <lambda>\n    lambda: client.predict(\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 500, in predict\n    ).result()\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1605, in result\n    return super().result(timeout=timeout)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 458, in result\n    return self.__get_result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 403, in __get_result\n    raise self._exception\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1209, in _inner\n    predictions = _predict(*data)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1329, in _predict\n    raise AppError(message=message, **result)\n", "gradio_client.exceptions.AppError: Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.\n"]}}
{"timestamp": "2025-09-02T17:23:35.986752", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 204, "request_id": "0cad8507-0151-4404-be54-3a7c6a056b60", "exception": {"type": "AppError", "message": "Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 165, in try_on_clothing\n    cached_result = self._get_cached_result(cache_key)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 168, in <lambda>\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 500, in predict\n    ).result()\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1605, in result\n    return super().result(timeout=timeout)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 458, in result\n    return self.__get_result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 403, in __get_result\n    raise self._exception\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1209, in _inner\n    predictions = _predict(*data)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1329, in _predict\n    raise AppError(message=message, **result)\n", "gradio_client.exceptions.AppError: Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.\n"]}}
{"timestamp": "2025-09-02T17:36:06.014766", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 204, "request_id": "0cad8507-0151-4404-be54-3a7c6a056b60", "exception": {"type": "AppError", "message": "Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 165, in try_on_clothing\n    cached_result = self._get_cached_result(cache_key)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 168, in <lambda>\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 500, in predict\n    ).result()\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1605, in result\n    return super().result(timeout=timeout)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 458, in result\n    return self.__get_result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 403, in __get_result\n    raise self._exception\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1209, in _inner\n    predictions = _predict(*data)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1329, in _predict\n    raise AppError(message=message, **result)\n", "gradio_client.exceptions.AppError: Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.\n"]}}
{"timestamp": "2025-09-02T17:41:10.193034", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 204, "request_id": "0cad8507-0151-4404-be54-3a7c6a056b60", "exception": {"type": "AppError", "message": "Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 165, in try_on_clothing\n    cached_result = self._get_cached_result(cache_key)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 168, in <lambda>\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 500, in predict\n    ).result()\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1605, in result\n    return super().result(timeout=timeout)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 458, in result\n    return self.__get_result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 403, in __get_result\n    raise self._exception\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1209, in _inner\n    predictions = _predict(*data)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1329, in _predict\n    raise AppError(message=message, **result)\n", "gradio_client.exceptions.AppError: Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.\n"]}}
{"timestamp": "2025-09-02T17:43:02.223169", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 204, "request_id": "0cad8507-0151-4404-be54-3a7c6a056b60", "exception": {"type": "AppError", "message": "Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 165, in try_on_clothing\n    cached_result = self._get_cached_result(cache_key)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 168, in <lambda>\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 500, in predict\n    ).result()\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1605, in result\n    return super().result(timeout=timeout)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 458, in result\n    return self.__get_result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 403, in __get_result\n    raise self._exception\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1209, in _inner\n    predictions = _predict(*data)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1329, in _predict\n    raise AppError(message=message, **result)\n", "gradio_client.exceptions.AppError: Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.\n"]}}
{"timestamp": "2025-09-02T17:54:16.034010", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 204, "request_id": "0cad8507-0151-4404-be54-3a7c6a056b60", "exception": {"type": "AppError", "message": "Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 165, in try_on_clothing\n    cached_result = self._get_cached_result(cache_key)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 168, in <lambda>\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 500, in predict\n    ).result()\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1605, in result\n    return super().result(timeout=timeout)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 458, in result\n    return self.__get_result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 403, in __get_result\n    raise self._exception\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1209, in _inner\n    predictions = _predict(*data)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1329, in _predict\n    raise AppError(message=message, **result)\n", "gradio_client.exceptions.AppError: Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.\n"]}}
{"timestamp": "2025-09-02T17:54:47.980461", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro na serialização: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "_serialize_value", "line": 205, "request_id": "31b84d39-c8e9-45a0-a4f8-a5b71f929e0f"}
{"timestamp": "2025-09-02T17:54:47.981460", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro ao definir chave img_process_b9c42823c5f78988048e47f8fdb3ea1e: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "set", "line": 286, "request_id": "31b84d39-c8e9-45a0-a4f8-a5b71f929e0f"}
{"timestamp": "2025-09-02T17:55:11.253705", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 204, "request_id": "31b84d39-c8e9-45a0-a4f8-a5b71f929e0f", "exception": {"type": "AppError", "message": "Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 165, in try_on_clothing\n    cached_result = self._get_cached_result(cache_key)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 168, in <lambda>\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 500, in predict\n    ).result()\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1605, in result\n    return super().result(timeout=timeout)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 458, in result\n    return self.__get_result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 403, in __get_result\n    raise self._exception\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1209, in _inner\n    predictions = _predict(*data)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1329, in _predict\n    raise AppError(message=message, **result)\n", "gradio_client.exceptions.AppError: Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.\n"]}}
{"timestamp": "2025-09-02T17:57:12.056120", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 204, "request_id": "31b84d39-c8e9-45a0-a4f8-a5b71f929e0f", "exception": {"type": "AppError", "message": "Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 165, in try_on_clothing\n    cached_result = self._get_cached_result(cache_key)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 168, in <lambda>\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 500, in predict\n    ).result()\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1605, in result\n    return super().result(timeout=timeout)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 458, in result\n    return self.__get_result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 403, in __get_result\n    raise self._exception\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1209, in _inner\n    predictions = _predict(*data)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1329, in _predict\n    raise AppError(message=message, **result)\n", "gradio_client.exceptions.AppError: Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.\n"]}}
{"timestamp": "2025-09-02T17:58:06.539035", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro na serialização: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "_serialize_value", "line": 205, "request_id": "42a2732d-09cf-4b73-97b3-867ab0b3dbed"}
{"timestamp": "2025-09-02T17:58:06.539901", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro ao definir chave img_process_b9c42823c5f78988048e47f8fdb3ea1e: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "set", "line": 286, "request_id": "42a2732d-09cf-4b73-97b3-867ab0b3dbed"}
{"timestamp": "2025-09-02T17:58:30.453287", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 216, "request_id": "42a2732d-09cf-4b73-97b3-867ab0b3dbed", "exception": {"type": "AppError", "message": "Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 172, in try_on_clothing\n    result = await asyncio.wait_for(\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 175, in <lambda>\n    lambda: client.predict(\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 500, in predict\n    ).result()\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1605, in result\n    return super().result(timeout=timeout)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 458, in result\n    return self.__get_result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 403, in __get_result\n    raise self._exception\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1209, in _inner\n    predictions = _predict(*data)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1329, in _predict\n    raise AppError(message=message, **result)\n", "gradio_client.exceptions.AppError: Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.\n"]}}
{"timestamp": "2025-09-02T18:02:18.223377", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro na serialização: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "_serialize_value", "line": 205, "request_id": "1a0e7383-f543-4505-8a58-170a4c8864d1"}
{"timestamp": "2025-09-02T18:02:18.223377", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro ao definir chave img_process_b9c42823c5f78988048e47f8fdb3ea1e: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "set", "line": 286, "request_id": "1a0e7383-f543-4505-8a58-170a4c8864d1"}
{"timestamp": "2025-09-02T18:02:40.351354", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 218, "request_id": "1a0e7383-f543-4505-8a58-170a4c8864d1", "exception": {"type": "AppError", "message": "Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 174, in try_on_clothing\n    result = await asyncio.wait_for(\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 177, in <lambda>\n    lambda: client.predict(\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 500, in predict\n    ).result()\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1605, in result\n    return super().result(timeout=timeout)\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 458, in result\n    return self.__get_result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py\", line 403, in __get_result\n    raise self._exception\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1209, in _inner\n    predictions = _predict(*data)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\venv\\lib\\site-packages\\gradio_client\\client.py\", line 1329, in _predict\n    raise AppError(message=message, **result)\n", "gradio_client.exceptions.AppError: Unlogged user is runnning out of daily ZeroGPU quotas. Signup for free on https://huggingface.co/join or login on https://huggingface.co/login to get more ZeroGPU quota now.\n"]}}
{"timestamp": "2025-09-02T18:12:40.203142", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 221, "request_id": "1a0e7383-f543-4505-8a58-170a4c8864d1", "exception": {"type": "AttributeError", "message": "'Client' object has no attribute 'file'", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 174, in try_on_clothing\n    result = await asyncio.wait_for(\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 179, in <lambda>\n    \"background\": client.file(person_temp_path),\n", "AttributeError: 'Client' object has no attribute 'file'\n"]}}
{"timestamp": "2025-09-02T18:13:58.496351", "level": "ERROR", "logger": "huggingface_service", "message": "Erro no virtual try-on", "module": "huggingface_service", "function": "try_on_clothing", "line": 221, "request_id": "1a0e7383-f543-4505-8a58-170a4c8864d1", "exception": {"type": "AttributeError", "message": "'Client' object has no attribute 'file'", "traceback": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 174, in try_on_clothing\n    result = await asyncio.wait_for(\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n", "  File \"C:\\Users\\<USER>\\Downloads\\trabalho-novo-main\\trabalho\\modulo\\huggingface_service.py\", line 179, in <lambda>\n    \"background\": file(person_temp_path),\n", "AttributeError: 'Client' object has no attribute 'file'\n"]}}
{"timestamp": "2025-09-02T18:15:01.500315", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro na serialização: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "_serialize_value", "line": 205, "request_id": "b8f8f41c-074d-4770-b486-951c00ebdb54"}
{"timestamp": "2025-09-02T18:15:01.500315", "level": "ERROR", "logger": "advanced_cache_service", "message": "Erro ao definir chave img_process_b9c42823c5f78988048e47f8fdb3ea1e: Object of type bytes is not JSON serializable", "module": "advanced_cache_service", "function": "set", "line": 286, "request_id": "b8f8f41c-074d-4770-b486-951c00ebdb54"}
