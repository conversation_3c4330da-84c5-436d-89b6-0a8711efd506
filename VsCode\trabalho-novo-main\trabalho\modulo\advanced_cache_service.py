"""
Serviço de cache avançado com suporte a Redis, TTL inteligente e compressão
"""
import asyncio
import json
import logging
import pickle
import time
import zlib
from typing import Any, Dict, Optional, Union
from abc import ABC, abstractmethod

import aioredis
from dependency_injection import ICacheService, singleton

logger = logging.getLogger(__name__)


class CacheStats:
    """Estatísticas do cache"""
    
    def __init__(self):
        self.hits = 0
        self.misses = 0
        self.sets = 0
        self.deletes = 0
        self.errors = 0
        self.start_time = time.time()
    
    @property
    def hit_rate(self) -> float:
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    @property
    def uptime(self) -> float:
        return time.time() - self.start_time
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'hits': self.hits,
            'misses': self.misses,
            'sets': self.sets,
            'deletes': self.deletes,
            'errors': self.errors,
            'hit_rate': self.hit_rate,
            'uptime_seconds': self.uptime
        }


class MemoryCache:
    """Cache em memória com LRU e TTL"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_order: Dict[str, float] = {}
    
    def _evict_expired(self):
        """Remove itens expirados"""
        current_time = time.time()
        expired_keys = []
        
        for key, data in self.cache.items():
            if data.get('expires_at', float('inf')) < current_time:
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_key(key)
    
    def _evict_lru(self):
        """Remove item menos recentemente usado"""
        if not self.access_order:
            return
        
        oldest_key = min(self.access_order.keys(), key=lambda k: self.access_order[k])
        self._remove_key(oldest_key)
    
    def _remove_key(self, key: str):
        """Remove chave do cache"""
        self.cache.pop(key, None)
        self.access_order.pop(key, None)
    
    def get(self, key: str) -> Optional[Any]:
        """Obtém valor do cache"""
        self._evict_expired()
        
        if key not in self.cache:
            return None
        
        data = self.cache[key]
        current_time = time.time()
        
        # Verifica expiração
        if data.get('expires_at', float('inf')) < current_time:
            self._remove_key(key)
            return None
        
        # Atualiza ordem de acesso
        self.access_order[key] = current_time
        return data['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """Define valor no cache"""
        self._evict_expired()
        
        # Remove se já existe
        if key in self.cache:
            self._remove_key(key)
        
        # Evict LRU se necessário
        while len(self.cache) >= self.max_size:
            self._evict_lru()
        
        # Adiciona novo item
        current_time = time.time()
        expires_at = current_time + ttl if ttl else None
        
        self.cache[key] = {
            'value': value,
            'created_at': current_time,
            'expires_at': expires_at
        }
        self.access_order[key] = current_time
    
    def delete(self, key: str) -> bool:
        """Remove chave do cache"""
        if key in self.cache:
            self._remove_key(key)
            return True
        return False
    
    def clear(self):
        """Limpa todo o cache"""
        self.cache.clear()
        self.access_order.clear()
    
    def size(self) -> int:
        """Retorna tamanho atual do cache"""
        self._evict_expired()
        return len(self.cache)


@singleton
class AdvancedCacheService(ICacheService):
    """Serviço de cache avançado com fallback para memória"""
    
    def __init__(self, redis_url: str = None, enable_compression: bool = True):
        self.redis_url = redis_url or "redis://localhost:6379"
        self.enable_compression = enable_compression
        self.redis_pool: Optional[aioredis.Redis] = None
        self.memory_cache = MemoryCache(max_size=1000)
        self.stats = CacheStats()
        self._connection_lock = asyncio.Lock()
        
        logger.info(f"AdvancedCacheService inicializado com Redis: {bool(redis_url)}")
    
    async def _get_redis_connection(self) -> Optional[aioredis.Redis]:
        """Obtém conexão Redis com lazy loading"""
        if self.redis_pool is None:
            async with self._connection_lock:
                if self.redis_pool is None:
                    try:
                        self.redis_pool = aioredis.from_url(
                            self.redis_url,
                            encoding="utf-8",
                            decode_responses=False,
                            max_connections=20,
                            retry_on_timeout=True
                        )
                        # Testa conexão
                        await self.redis_pool.ping()
                        logger.info("Conexão Redis estabelecida")
                    except Exception as e:
                        logger.warning(f"Falha ao conectar Redis: {e}. Usando cache em memória.")
                        self.redis_pool = None
        
        return self.redis_pool
    
    def _serialize_value(self, value: Any) -> bytes:
        """Serializa valor para armazenamento"""
        try:
            # Tenta JSON primeiro (mais eficiente)
            if isinstance(value, (dict, list, str, int, float, bool)) or value is None:
                data = json.dumps(value, ensure_ascii=False).encode('utf-8')
                prefix = b'json:'
            else:
                # Fallback para pickle
                data = pickle.dumps(value)
                prefix = b'pickle:'
            # Se ainda for bytes puro (e não JSON), serialize como pickle
            if isinstance(value, (bytes, bytearray)):
                data = pickle.dumps(value)
                prefix = b'pickle:'
            
            # Comprime se habilitado e vantajoso
            if self.enable_compression and len(data) > 1024:
                compressed = zlib.compress(data, level=6)
                if len(compressed) < len(data) * 0.9:  # Só comprime se reduzir pelo menos 10%
                    return b'compressed:' + prefix + compressed
            
            return prefix + data
            
        except Exception as e:
            logger.error(f"Erro na serialização: {e}")
            raise
    
    def _deserialize_value(self, data: bytes) -> Any:
        """Deserializa valor do armazenamento"""
        try:
            if data.startswith(b'compressed:'):
                data = data[11:]  # Remove 'compressed:' prefix
                if data.startswith(b'json:'):
                    data = zlib.decompress(data[5:])
                    return json.loads(data.decode('utf-8'))
                elif data.startswith(b'pickle:'):
                    data = zlib.decompress(data[7:])
                    return pickle.loads(data)
            elif data.startswith(b'json:'):
                return json.loads(data[5:].decode('utf-8'))
            elif data.startswith(b'pickle:'):
                return pickle.loads(data[7:])
            else:
                # Fallback para formato antigo
                return pickle.loads(data)
                
        except Exception as e:
            logger.error(f"Erro na deserialização: {e}")
            raise
    
    async def get(self, key: str) -> Optional[Any]:
        """Obtém valor do cache"""
        try:
            # Tenta Redis primeiro
            redis = await self._get_redis_connection()
            if redis:
                try:
                    data = await redis.get(key)
                    if data:
                        self.stats.hits += 1
                        return self._deserialize_value(data)
                except Exception as e:
                    logger.warning(f"Erro ao acessar Redis: {e}")
                    self.stats.errors += 1
            
            # Fallback para cache em memória
            value = self.memory_cache.get(key)
            if value is not None:
                self.stats.hits += 1
                return value
            
            self.stats.misses += 1
            return None
            
        except Exception as e:
            logger.error(f"Erro ao obter chave {key}: {e}")
            self.stats.errors += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """Define valor no cache"""
        try:
            serialized_value = self._serialize_value(value)
            
            # Tenta Redis primeiro
            redis = await self._get_redis_connection()
            if redis:
                try:
                    if ttl:
                        await redis.setex(key, ttl, serialized_value)
                    else:
                        await redis.set(key, serialized_value)
                    
                    self.stats.sets += 1
                    return True
                except Exception as e:
                    logger.warning(f"Erro ao definir no Redis: {e}")
                    self.stats.errors += 1
            
            # Fallback para cache em memória
            self.memory_cache.set(key, value, ttl)
            self.stats.sets += 1
            return True
            
        except Exception as e:
            logger.error(f"Erro ao definir chave {key}: {e}")
            self.stats.errors += 1
            return False
    
    async def delete(self, key: str) -> bool:
        """Remove chave do cache"""
        try:
            deleted = False
            
            # Remove do Redis
            redis = await self._get_redis_connection()
            if redis:
                try:
                    result = await redis.delete(key)
                    deleted = result > 0
                except Exception as e:
                    logger.warning(f"Erro ao deletar do Redis: {e}")
                    self.stats.errors += 1
            
            # Remove do cache em memória
            memory_deleted = self.memory_cache.delete(key)
            
            if deleted or memory_deleted:
                self.stats.deletes += 1
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Erro ao deletar chave {key}: {e}")
            self.stats.errors += 1
            return False
    
    async def clear(self) -> bool:
        """Limpa todo o cache"""
        try:
            # Limpa Redis
            redis = await self._get_redis_connection()
            if redis:
                try:
                    await redis.flushdb()
                except Exception as e:
                    logger.warning(f"Erro ao limpar Redis: {e}")
                    self.stats.errors += 1
            
            # Limpa cache em memória
            self.memory_cache.clear()
            
            logger.info("Cache limpo com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao limpar cache: {e}")
            self.stats.errors += 1
            return False
    
    async def exists(self, key: str) -> bool:
        """Verifica se chave existe no cache"""
        try:
            # Verifica Redis
            redis = await self._get_redis_connection()
            if redis:
                try:
                    result = await redis.exists(key)
                    if result:
                        return True
                except Exception as e:
                    logger.warning(f"Erro ao verificar Redis: {e}")
            
            # Verifica cache em memória
            return self.memory_cache.get(key) is not None
            
        except Exception as e:
            logger.error(f"Erro ao verificar chave {key}: {e}")
            return False
    
    async def get_many(self, keys: list) -> Dict[str, Any]:
        """Obtém múltiplas chaves do cache"""
        result = {}
        
        try:
            # Tenta Redis primeiro
            redis = await self._get_redis_connection()
            if redis and keys:
                try:
                    values = await redis.mget(keys)
                    for key, value in zip(keys, values):
                        if value:
                            result[key] = self._deserialize_value(value)
                            self.stats.hits += 1
                        else:
                            self.stats.misses += 1
                except Exception as e:
                    logger.warning(f"Erro ao obter múltiplas chaves do Redis: {e}")
                    self.stats.errors += 1
            
            # Fallback para cache em memória para chaves não encontradas
            missing_keys = [k for k in keys if k not in result]
            for key in missing_keys:
                value = self.memory_cache.get(key)
                if value is not None:
                    result[key] = value
                    self.stats.hits += 1
                else:
                    self.stats.misses += 1
            
            return result
            
        except Exception as e:
            logger.error(f"Erro ao obter múltiplas chaves: {e}")
            self.stats.errors += 1
            return {}
    
    async def set_many(self, mapping: Dict[str, Any], ttl: int = None) -> bool:
        """Define múltiplas chaves no cache"""
        try:
            # Serializa todos os valores
            serialized_mapping = {}
            for key, value in mapping.items():
                serialized_mapping[key] = self._serialize_value(value)
            
            # Tenta Redis primeiro
            redis = await self._get_redis_connection()
            if redis and serialized_mapping:
                try:
                    pipe = redis.pipeline()
                    for key, value in serialized_mapping.items():
                        if ttl:
                            pipe.setex(key, ttl, value)
                        else:
                            pipe.set(key, value)
                    await pipe.execute()
                    
                    self.stats.sets += len(mapping)
                    return True
                except Exception as e:
                    logger.warning(f"Erro ao definir múltiplas chaves no Redis: {e}")
                    self.stats.errors += 1
            
            # Fallback para cache em memória
            for key, value in mapping.items():
                self.memory_cache.set(key, value, ttl)
            
            self.stats.sets += len(mapping)
            return True
            
        except Exception as e:
            logger.error(f"Erro ao definir múltiplas chaves: {e}")
            self.stats.errors += 1
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do cache"""
        base_stats = self.stats.to_dict()
        base_stats.update({
            'memory_cache_size': self.memory_cache.size(),
            'redis_connected': self.redis_pool is not None,
            'compression_enabled': self.enable_compression
        })
        return base_stats
    
    async def close(self):
        """Fecha conexões do cache"""
        if self.redis_pool:
            await self.redis_pool.close()
            self.redis_pool = None
        
        self.memory_cache.clear()
        logger.info("AdvancedCacheService fechado")
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
