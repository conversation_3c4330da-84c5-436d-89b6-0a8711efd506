"""
Serviços de processamento otimizados para extração de pose, segmentação e inpainting
Modularizados para melhor performance e reutilização
"""
import cv2
import numpy as np
from PIL import Image
from typing import Optional, Tuple, Dict, Any
import asyncio
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class ProcessingService(ABC):
    """Classe base para serviços de processamento"""
    
    def __init__(self):
        self._initialized = False
    
    @abstractmethod
    async def initialize(self):
        """Inicializa o serviço (carregamento de modelos, etc.)"""
        pass
    
    @abstractmethod
    async def process(self, image: np.ndarray) -> Any:
        """Processa a imagem"""
        pass
    
    async def ensure_initialized(self):
        """Garante que o serviço está inicializado"""
        if not self._initialized:
            await self.initialize()
            self._initialized = True

class PoseEstimationService(ProcessingService):
    """Serviço de estimação de pose usando MediaPipe"""
    
    def __init__(self):
        super().__init__()
        self._mp = None
        self._pose = None
        self._drawing_utils = None
    
    async def initialize(self):
        """Inicializa MediaPipe Pose"""
        try:
            import mediapipe as mp
            self._mp = mp
            self._pose = mp.solutions.pose.Pose(
                static_image_mode=True,
                model_complexity=2,  # Maior precisão
                enable_segmentation=True,
                min_detection_confidence=0.5
            )
            self._drawing_utils = mp.solutions.drawing_utils
            logger.info("PoseEstimationService inicializado com MediaPipe")
        except ImportError:
            logger.warning("MediaPipe não disponível, usando fallback")
            self._mp = None
    
    async def process(self, image_bgr: np.ndarray) -> Dict[str, Any]:
        """
        Extrai pose da imagem
        
        Args:
            image_bgr: Imagem em formato BGR
            
        Returns:
            Dict contendo pose_map, landmarks e confiança
        """
        await self.ensure_initialized()
        
        if self._mp is None:
            return await self._fallback_pose_estimation(image_bgr)
        
        # Converte BGR para RGB
        image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)
        
        # Processa pose
        results = self._pose.process(image_rgb)
        
        # Cria mapa visual da pose
        pose_map = image_bgr.copy()
        confidence = 0.0
        landmarks_data = None
        
        if results.pose_landmarks:
            # Desenha landmarks
            self._drawing_utils.draw_landmarks(
                pose_map,
                results.pose_landmarks,
                self._mp.solutions.pose.POSE_CONNECTIONS,
                self._drawing_utils.DrawingSpec(color=(0, 255, 0), thickness=2, circle_radius=2),
                self._drawing_utils.DrawingSpec(color=(255, 0, 0), thickness=2)
            )
            
            # Calcula confiança média
            landmarks = results.pose_landmarks.landmark
            confidences = [lm.visibility for lm in landmarks if hasattr(lm, 'visibility')]
            confidence = np.mean(confidences) if confidences else 0.0
            
            # Extrai dados dos landmarks
            landmarks_data = [
                {"x": lm.x, "y": lm.y, "z": lm.z, "visibility": getattr(lm, 'visibility', 0.0)}
                for lm in landmarks
            ]
        
        return {
            "pose_map": pose_map,
            "landmarks": landmarks_data,
            "confidence": confidence,
            "segmentation_mask": results.segmentation_mask if hasattr(results, 'segmentation_mask') else None
        }
    
    async def _fallback_pose_estimation(self, image_bgr: np.ndarray) -> Dict[str, Any]:
        """Fallback simples quando MediaPipe não está disponível"""
        logger.warning("Usando fallback para estimação de pose")
        
        # Cria um mapa de pose básico (contornos)
        gray = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        pose_map = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
        
        return {
            "pose_map": pose_map,
            "landmarks": None,
            "confidence": 0.1,
            "segmentation_mask": None
        }

class SegmentationService(ProcessingService):
    """Serviço de segmentação humana"""
    
    def __init__(self):
        super().__init__()
        self._mp = None
        self._selfie_segmentation = None
    
    async def initialize(self):
        """Inicializa MediaPipe Selfie Segmentation"""
        try:
            import mediapipe as mp
            self._mp = mp
            self._selfie_segmentation = mp.solutions.selfie_segmentation.SelfieSegmentation(
                model_selection=1  # Modelo mais preciso
            )
            logger.info("SegmentationService inicializado com MediaPipe")
        except ImportError:
            logger.warning("MediaPipe não disponível, usando fallback")
            self._mp = None
    
    async def process(self, image_bgr: np.ndarray) -> Dict[str, Any]:
        """
        Segmenta pessoa na imagem
        
        Args:
            image_bgr: Imagem em formato BGR
            
        Returns:
            Dict contendo máscara binária e confiança
        """
        await self.ensure_initialized()
        
        if self._mp is None:
            return await self._fallback_segmentation(image_bgr)
        
        # Converte BGR para RGB
        image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)
        
        # Processa segmentação
        results = self._selfie_segmentation.process(image_rgb)
        
        # Converte máscara para binária
        mask_float = results.segmentation_mask
        mask_binary = (mask_float > 0.5).astype(np.uint8) * 255
        
        # Aplica operações morfológicas para limpar a máscara
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        mask_cleaned = cv2.morphologyEx(mask_binary, cv2.MORPH_CLOSE, kernel)
        mask_cleaned = cv2.morphologyEx(mask_cleaned, cv2.MORPH_OPEN, kernel)
        
        # Calcula confiança baseada na área segmentada
        total_pixels = mask_binary.shape[0] * mask_binary.shape[1]
        person_pixels = np.sum(mask_binary > 0)
        confidence = min(person_pixels / total_pixels * 2, 1.0)  # Normaliza
        
        return {
            "mask": mask_cleaned,
            "mask_raw": mask_binary,
            "confidence": confidence,
            "person_area_ratio": person_pixels / total_pixels
        }
    
    async def _fallback_segmentation(self, image_bgr: np.ndarray) -> Dict[str, Any]:
        """Fallback usando segmentação por cor (HSV)"""
        logger.warning("Usando fallback para segmentação")
        
        # Converte para HSV
        hsv = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2HSV)
        
        # Define ranges para tons de pele (aproximado)
        lower_skin = np.array([0, 20, 70])
        upper_skin = np.array([20, 255, 255])
        
        # Cria máscara básica
        mask = cv2.inRange(hsv, lower_skin, upper_skin)
        
        # Aplica operações morfológicas
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (11, 11))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        return {
            "mask": mask,
            "mask_raw": mask,
            "confidence": 0.3,  # Baixa confiança para fallback
            "person_area_ratio": np.sum(mask > 0) / (mask.shape[0] * mask.shape[1])
        }

class InpaintingService(ProcessingService):
    """Serviço de inpainting para remoção de roupas"""
    
    def __init__(self):
        super().__init__()
    
    async def initialize(self):
        """Inicializa o serviço (OpenCV já disponível)"""
        logger.info("InpaintingService inicializado")
    
    async def process(self, image_bgr: np.ndarray, mask: np.ndarray, 
                     method: str = "telea") -> Dict[str, Any]:
        """
        Aplica inpainting na imagem
        
        Args:
            image_bgr: Imagem original
            mask: Máscara das áreas a serem inpaintadas
            method: Método de inpainting ("telea" ou "ns")
            
        Returns:
            Dict contendo imagem inpaintada e métricas
        """
        await self.ensure_initialized()
        
        # Inverte a máscara (inpainting precisa de áreas brancas)
        inpaint_mask = cv2.bitwise_not(mask)
        
        # Aplica inpainting
        if method == "ns":
            inpainted = cv2.inpaint(image_bgr, inpaint_mask, 3, cv2.INPAINT_NS)
        else:  # telea (padrão)
            inpainted = cv2.inpaint(image_bgr, inpaint_mask, 3, cv2.INPAINT_TELEA)
        
        # Calcula métricas de qualidade
        inpaint_area_ratio = np.sum(inpaint_mask > 0) / (inpaint_mask.shape[0] * inpaint_mask.shape[1])
        
        # Suaviza bordas entre área inpaintada e original
        blurred_mask = cv2.GaussianBlur(mask.astype(np.float32), (5, 5), 0) / 255.0
        blurred_mask = np.stack([blurred_mask] * 3, axis=-1)
        
        # Mistura suave
        result = (image_bgr * blurred_mask + inpainted * (1 - blurred_mask)).astype(np.uint8)
        
        return {
            "inpainted_image": result,
            "inpainted_raw": inpainted,
            "inpaint_area_ratio": inpaint_area_ratio,
            "method_used": method
        }

class ProcessingManager:
    """Gerenciador central dos serviços de processamento"""
    
    def __init__(self):
        self.pose_service = PoseEstimationService()
        self.segmentation_service = SegmentationService()
        self.inpainting_service = InpaintingService()
    
    async def initialize_all(self):
        """Inicializa todos os serviços"""
        await asyncio.gather(
            self.pose_service.initialize(),
            self.segmentation_service.initialize(),
            self.inpainting_service.initialize()
        )
        logger.info("Todos os serviços de processamento inicializados")
    
    async def process_user_image(self, image_bgr: np.ndarray) -> Dict[str, Any]:
        """
        Processa imagem do usuário completa (Fase 1)
        
        Args:
            image_bgr: Imagem do usuário em BGR
            
        Returns:
            Dict com todos os artefatos processados
        """
        logger.info("Iniciando processamento completo da imagem do usuário")
        
        # Processa em paralelo quando possível
        pose_task = self.pose_service.process(image_bgr)
        segmentation_task = self.segmentation_service.process(image_bgr)
        
        pose_result, segmentation_result = await asyncio.gather(
            pose_task, segmentation_task
        )
        
        # Inpainting depende da segmentação
        inpainting_result = await self.inpainting_service.process(
            image_bgr, segmentation_result["mask"]
        )
        
        return {
            "pose": pose_result,
            "segmentation": segmentation_result,
            "inpainting": inpainting_result,
            "processing_quality": {
                "pose_confidence": pose_result["confidence"],
                "segmentation_confidence": segmentation_result["confidence"],
                "inpaint_area_ratio": inpainting_result["inpaint_area_ratio"]
            }
        }

# Instância global do gerenciador
_processing_manager: Optional[ProcessingManager] = None

def get_processing_manager() -> ProcessingManager:
    """Retorna instância singleton do gerenciador de processamento"""
    global _processing_manager
    if _processing_manager is None:
        _processing_manager = ProcessingManager()
    return _processing_manager
