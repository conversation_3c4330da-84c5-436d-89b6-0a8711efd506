"""
Sistema de monitoramento e métricas para VesteAI
Inclui health checks, métricas de performance e alertas
"""
import asyncio
import json
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import logging

from config_manager import get_config
from advanced_logging import get_logger
from dependency_injection import singleton

logger = get_logger(__name__)


@dataclass
class HealthCheckResult:
    """Resultado de um health check"""
    name: str
    status: str  # "healthy", "warning", "critical"
    message: str
    response_time_ms: float
    timestamp: str
    details: Dict[str, Any] = None


@dataclass
class MetricPoint:
    """Ponto de métrica"""
    name: str
    value: float
    timestamp: float
    tags: Dict[str, str] = None


class MetricsCollector:
    """Coletor de métricas do sistema"""
    
    def __init__(self, max_points: int = 1000):
        self.max_points = max_points
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_points))
        self._lock = threading.Lock()
    
    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """Registra uma métrica"""
        with self._lock:
            point = MetricPoint(
                name=name,
                value=value,
                timestamp=time.time(),
                tags=tags or {}
            )
            self.metrics[name].append(point)
    
    def get_metrics(self, name: str, since: float = None) -> List[MetricPoint]:
        """Obtém métricas por nome"""
        with self._lock:
            points = list(self.metrics[name])
            if since:
                points = [p for p in points if p.timestamp >= since]
            return points
    
    def get_metric_summary(self, name: str, window_seconds: int = 300) -> Dict[str, Any]:
        """Obtém resumo de uma métrica"""
        since = time.time() - window_seconds
        points = self.get_metrics(name, since)
        
        if not points:
            return {"count": 0, "avg": 0, "min": 0, "max": 0}
        
        values = [p.value for p in points]
        return {
            "count": len(values),
            "avg": sum(values) / len(values),
            "min": min(values),
            "max": max(values),
            "latest": values[-1] if values else 0
        }
    
    def get_all_metrics_summary(self) -> Dict[str, Dict[str, Any]]:
        """Obtém resumo de todas as métricas"""
        with self._lock:
            return {
                name: self.get_metric_summary(name)
                for name in self.metrics.keys()
            }


class SystemMonitor:
    """Monitor de recursos do sistema"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self, interval: float = 30.0):
        """Inicia monitoramento do sistema"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info("Monitoramento do sistema iniciado")
    
    def stop_monitoring(self):
        """Para monitoramento do sistema"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Monitoramento do sistema parado")
    
    def _monitor_loop(self, interval: float):
        """Loop principal de monitoramento"""
        while self.monitoring:
            try:
                self._collect_system_metrics()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"Erro no monitoramento do sistema: {e}")
                time.sleep(interval)
    
    def _collect_system_metrics(self):
        """Coleta métricas do sistema"""
        try:
            # CPU
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics_collector.record_metric("system.cpu.percent", cpu_percent)
            
            # Memória
            memory = psutil.virtual_memory()
            self.metrics_collector.record_metric("system.memory.percent", memory.percent)
            self.metrics_collector.record_metric("system.memory.used_gb", memory.used / (1024**3))
            self.metrics_collector.record_metric("system.memory.available_gb", memory.available / (1024**3))
            
            # Disco
            disk = psutil.disk_usage('/')
            self.metrics_collector.record_metric("system.disk.percent", disk.percent)
            self.metrics_collector.record_metric("system.disk.used_gb", disk.used / (1024**3))
            self.metrics_collector.record_metric("system.disk.free_gb", disk.free / (1024**3))
            
            # Rede (se disponível)
            try:
                net_io = psutil.net_io_counters()
                self.metrics_collector.record_metric("system.network.bytes_sent", net_io.bytes_sent)
                self.metrics_collector.record_metric("system.network.bytes_recv", net_io.bytes_recv)
            except:
                pass  # Nem sempre disponível
            
        except Exception as e:
            logger.warning(f"Erro ao coletar métricas do sistema: {e}")


class HealthChecker:
    """Sistema de health checks"""
    
    def __init__(self):
        self.checks: Dict[str, Callable] = {}
        self.last_results: Dict[str, HealthCheckResult] = {}
    
    def register_check(self, name: str, check_func: Callable):
        """Registra um health check"""
        self.checks[name] = check_func
        logger.info(f"Health check registrado: {name}")
    
    async def run_check(self, name: str) -> HealthCheckResult:
        """Executa um health check específico"""
        if name not in self.checks:
            return HealthCheckResult(
                name=name,
                status="critical",
                message="Health check não encontrado",
                response_time_ms=0,
                timestamp=datetime.now().isoformat()
            )
        
        start_time = time.time()
        try:
            check_func = self.checks[name]
            
            # Executa check com timeout
            if asyncio.iscoroutinefunction(check_func):
                result = await asyncio.wait_for(check_func(), timeout=30.0)
            else:
                result = await asyncio.get_event_loop().run_in_executor(
                    None, check_func
                )
            
            response_time = (time.time() - start_time) * 1000
            
            if isinstance(result, dict):
                health_result = HealthCheckResult(
                    name=name,
                    status=result.get("status", "healthy"),
                    message=result.get("message", "OK"),
                    response_time_ms=response_time,
                    timestamp=datetime.now().isoformat(),
                    details=result.get("details")
                )
            else:
                health_result = HealthCheckResult(
                    name=name,
                    status="healthy" if result else "critical",
                    message="OK" if result else "Check failed",
                    response_time_ms=response_time,
                    timestamp=datetime.now().isoformat()
                )
            
            self.last_results[name] = health_result
            return health_result
            
        except asyncio.TimeoutError:
            response_time = (time.time() - start_time) * 1000
            result = HealthCheckResult(
                name=name,
                status="critical",
                message="Health check timeout",
                response_time_ms=response_time,
                timestamp=datetime.now().isoformat()
            )
            self.last_results[name] = result
            return result
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            result = HealthCheckResult(
                name=name,
                status="critical",
                message=f"Health check error: {str(e)}",
                response_time_ms=response_time,
                timestamp=datetime.now().isoformat()
            )
            self.last_results[name] = result
            return result
    
    async def run_all_checks(self) -> Dict[str, HealthCheckResult]:
        """Executa todos os health checks"""
        results = {}
        
        # Executa checks em paralelo
        tasks = [
            self.run_check(name) for name in self.checks.keys()
        ]
        
        if tasks:
            check_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(check_results):
                check_name = list(self.checks.keys())[i]
                if isinstance(result, Exception):
                    results[check_name] = HealthCheckResult(
                        name=check_name,
                        status="critical",
                        message=f"Exception: {str(result)}",
                        response_time_ms=0,
                        timestamp=datetime.now().isoformat()
                    )
                else:
                    results[check_name] = result
        
        return results
    
    def get_overall_status(self) -> str:
        """Obtém status geral do sistema"""
        if not self.last_results:
            return "unknown"
        
        statuses = [result.status for result in self.last_results.values()]
        
        if "critical" in statuses:
            return "critical"
        elif "warning" in statuses:
            return "warning"
        else:
            return "healthy"


@singleton
class MonitoringSystem:
    """Sistema principal de monitoramento"""
    
    def __init__(self):
        self.config = get_config()
        self.metrics_collector = MetricsCollector()
        self.system_monitor = SystemMonitor(self.metrics_collector)
        self.health_checker = HealthChecker()
        
        # Registra health checks padrão
        self._register_default_health_checks()
        
        logger.info("Sistema de monitoramento inicializado")
    
    def _register_default_health_checks(self):
        """Registra health checks padrão"""
        
        def check_memory():
            """Check de memória"""
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                return {
                    "status": "critical",
                    "message": f"Memória crítica: {memory.percent:.1f}%",
                    "details": {"memory_percent": memory.percent}
                }
            elif memory.percent > 80:
                return {
                    "status": "warning",
                    "message": f"Memória alta: {memory.percent:.1f}%",
                    "details": {"memory_percent": memory.percent}
                }
            else:
                return {
                    "status": "healthy",
                    "message": f"Memória OK: {memory.percent:.1f}%",
                    "details": {"memory_percent": memory.percent}
                }
        
        def check_disk():
            """Check de disco"""
            disk = psutil.disk_usage('/')
            if disk.percent > 95:
                return {
                    "status": "critical",
                    "message": f"Disco crítico: {disk.percent:.1f}%",
                    "details": {"disk_percent": disk.percent}
                }
            elif disk.percent > 85:
                return {
                    "status": "warning",
                    "message": f"Disco alto: {disk.percent:.1f}%",
                    "details": {"disk_percent": disk.percent}
                }
            else:
                return {
                    "status": "healthy",
                    "message": f"Disco OK: {disk.percent:.1f}%",
                    "details": {"disk_percent": disk.percent}
                }
        
        async def check_cache():
            """Check do cache"""
            try:
                from advanced_cache_service import AdvancedCacheService
                cache = AdvancedCacheService(redis_url=None)
                
                # Testa operação básica
                test_key = "health_check_test"
                await cache.set(test_key, "test_value", ttl=60)
                value = await cache.get(test_key)
                await cache.delete(test_key)
                
                if value == "test_value":
                    return {
                        "status": "healthy",
                        "message": "Cache funcionando",
                        "details": cache.get_stats()
                    }
                else:
                    return {
                        "status": "critical",
                        "message": "Cache não está funcionando corretamente"
                    }
                    
            except Exception as e:
                return {
                    "status": "critical",
                    "message": f"Erro no cache: {str(e)}"
                }
        
        # Registra os checks
        self.health_checker.register_check("memory", check_memory)
        self.health_checker.register_check("disk", check_disk)
        self.health_checker.register_check("cache", check_cache)
    
    def start_monitoring(self):
        """Inicia monitoramento"""
        if self.config.enable_metrics:
            self.system_monitor.start_monitoring()
    
    def stop_monitoring(self):
        """Para monitoramento"""
        self.system_monitor.stop_monitoring()
    
    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """Registra métrica"""
        self.metrics_collector.record_metric(name, value, tags)
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Obtém status de saúde completo"""
        health_results = await self.health_checker.run_all_checks()
        overall_status = self.health_checker.get_overall_status()
        
        return {
            "overall_status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "checks": {name: asdict(result) for name, result in health_results.items()}
        }
    
    def get_metrics_dashboard(self) -> Dict[str, Any]:
        """Obtém dashboard de métricas"""
        return {
            "timestamp": datetime.now().isoformat(),
            "system_metrics": self.metrics_collector.get_all_metrics_summary(),
            "uptime_seconds": time.time() - getattr(self, '_start_time', time.time())
        }
    
    def __enter__(self):
        self._start_time = time.time()
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop_monitoring()


# Instância global
def get_monitoring_system() -> MonitoringSystem:
    """Obtém instância do sistema de monitoramento"""
    return MonitoringSystem()
