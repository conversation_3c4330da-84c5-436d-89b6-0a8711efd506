"""
Serviço de Try-On otimizado em duas fases - VERSÃO ATUALIZADA
- Fase 1: criar modelo base do usuário (pose, máscara, imagem limpa) e persistir.
- Fase 2: aplicar roupa usando o modelo base (processamento leve).

IMPORTANTE: Este módulo foi refatorado. Use user_model_service.py para a nova API.
Mantido para compatibilidade com código existente.
"""
import os
import json
import uuid
import shutil
from pathlib import Path
from typing import Dict, Any, Optional
import asyncio
from PIL import Image
import numpy as np
import cv2
import logging

from config_manager import get_config
from huggingface_service import get_hf_service
from user_model_service import get_user_model_service

logger = logging.getLogger(__name__)


class UserModelStore:
    """Persistência simples do modelo base em disco."""
    def __init__(self, base_dir: Optional[Path] = None):
        cfg = get_config()
        self.base_dir = base_dir or (cfg.cache_dir / "user_models")
        self.base_dir.mkdir(parents=True, exist_ok=True)

    def create(self) -> str:
        model_id = f"user_model_{uuid.uuid4().hex[:8]}"
        (self.base_dir / model_id).mkdir(parents=True, exist_ok=True)
        return model_id

    def path(self, model_id: str) -> Path:
        return self.base_dir / model_id

    def save_image(self, model_id: str, name: str, image: Image.Image) -> Path:
        out = self.path(model_id) / name
        image.save(out, format="PNG")
        return out

    def save_array(self, model_id: str, name: str, arr: np.ndarray) -> Path:
        out = self.path(model_id) / name
        cv2.imwrite(str(out), arr)
        return out

    def save_meta(self, model_id: str, meta: Dict[str, Any]) -> Path:
        out = self.path(model_id) / "meta.json"
        with open(out, "w", encoding="utf-8") as f:
            json.dump(meta, f, ensure_ascii=False, indent=2)
        return out

    def exists(self, model_id: str) -> bool:
        return (self.base_dir / model_id).exists()


class TryOnService:
    def __init__(self):
        self.store = UserModelStore()
        # Tenta carregar MediaPipe, cai no básico se indisponível
        try:
            import mediapipe as mp  # type: ignore
            self._mp = mp
        except Exception:
            self._mp = None

    def _estimate_pose(self, image_bgr: np.ndarray) -> np.ndarray:
        """Gera um mapa de pose. Usa MediaPipe Pose se disponível; caso contrário, fallback simples."""
        if self._mp is not None:
            mp_pose = self._mp.solutions.pose
            mp_drawing = self._mp.solutions.drawing_utils
            pose_img = image_bgr.copy()
            with mp_pose.Pose(static_image_mode=True) as pose:
                results = pose.process(cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB))
                if results.pose_landmarks:
                    mp_drawing.draw_landmarks(
                        pose_img,
                        results.pose_landmarks,
                        mp_pose.POSE_CONNECTIONS,
                        mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2, circle_radius=2),
                        mp_drawing.DrawingSpec(color=(255, 0, 0), thickness=2)
                    )
            return pose_img
        # Fallback básico
        gray = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2GRAY)
        return gray

    def _segment_person(self, image_bgr: np.ndarray) -> np.ndarray:
        """Gera máscara de pessoa. Usa SelfieSegmentation se disponível; senão, fallback HSV."""
        if self._mp is not None:
            mp_selfie = self._mp.solutions.selfie_segmentation
            with mp_selfie.SelfieSegmentation(model_selection=1) as segmenter:
                res = segmenter.process(cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB))
                mask_float = res.segmentation_mask
                mask = (mask_float > 0.5).astype(np.uint8) * 255
                return mask
        # Fallback simples por HSV
        hsv = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2HSV)
        lower = np.array([0, 10, 40])
        upper = np.array([179, 255, 255])
        mask = cv2.inRange(hsv, lower, upper)
        return mask

    def _inpaint_clothes(self, image_bgr: np.ndarray, mask: np.ndarray) -> np.ndarray:
        # Inpainting básico (telea) como proxy de imagem limpa
        inpainted = cv2.inpaint(image_bgr, cv2.bitwise_not(mask), 3, cv2.INPAINT_TELEA)
        return inpainted

    def create_user_model(self, user_image: Image.Image) -> Dict[str, Any]:
        model_id = self.store.create()
        # Converte para BGR
        bgr = cv2.cvtColor(np.array(user_image.convert('RGB')), cv2.COLOR_RGB2BGR)

        pose_map = self._estimate_pose(bgr)
        mask = self._segment_person(bgr)
        clean = self._inpaint_clothes(bgr, mask)

        pose_path = self.store.save_array(model_id, "pose.png", pose_map)
        mask_path = self.store.save_array(model_id, "mask.png", mask)
        clean_path = self.store.save_array(model_id, "clean.png", clean)
        original_path = self.store.save_image(model_id, "original.png", user_image)

        meta = {
            "model_id": model_id,
            "artifacts": {
                "pose": str(pose_path),
                "mask": str(mask_path),
                "clean": str(clean_path),
                "original": str(original_path)
            }
        }
        self.store.save_meta(model_id, meta)
        return meta

    async def try_on(self, model_id: str, garment_image: Image.Image) -> Optional[str]:
        if not self.store.exists(model_id):
            raise ValueError("Modelo base não encontrado")

        # Neste MVP, chamamos o VTON com original como background (rápido, já em disco)
        orig_path = self.store.path(model_id) / "original.png"

        # Salva roupa temporária
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as tmp:
            garment_image.save(tmp.name, format="PNG")
            garment_path = tmp.name

        # Reaproveita o serviço HF (usa token se houver)
        hf = get_hf_service()
        # Chama a API existente, passando PIL em memória (serviço lida com temp files)
        result = await hf.try_on_clothing(Image.open(orig_path), garment_image)
        # result é caminho do arquivo gerado
        return result


