import os
import pandas as pd
import cv2
import numpy as np


def recomendar_roupas(dicionario):
    base_dir = os.path.dirname(__file__)
    # Tentativas de caminho para o CSV (compatível com app.py)
    candidate_paths = [
        os.path.abspath(os.path.join(base_dir, '../../data/catalogo_roupas.csv')),
        os.path.abspath(os.path.join(base_dir, '../data/catalogo_roupas.csv')),
        os.path.abspath(os.path.join(os.getcwd(), 'data', 'catalogo_roupas.csv')),
        os.path.abspath('catalogo_roupas.csv')
    ]

    caminho_csv = next((p for p in candidate_paths if os.path.exists(p)), None)
    if not caminho_csv:
        print("❌ Arquivo CSV do catálogo não encontrado.")
        return pd.DataFrame()

    print("\nLendo CSV em:", caminho_csv)
    catalogo = pd.read_csv(caminho_csv)

    # Normaliza colunas
    catalogo.columns = catalogo.columns.str.strip().str.lower()

    roupas_filtradas = catalogo.copy()

    def classificar_paleta(medidas):
        subtom = (medidas.get("Subtom") or '').capitalize()
        contraste = medidas.get("Classificação") or ''
        intensidade = medidas.get("Intensidade") or ''
        profundidade = medidas.get("Profundidade") or ''

        if subtom == "Quente":
            if intensidade == "Alta" and profundidade == "Claro":
                return "Primavera Brilhante", roupas_filtradas[roupas_filtradas['estação'].str.contains("primavera brilhante", case=False, na=False)]
            if intensidade == "Baixa":
                if profundidade == "Escuro":
                    return "Outono Suave", roupas_filtradas[roupas_filtradas['estação'].str.contains("outono suave", case=False, na=False)]
                return "Primavera Suave", roupas_filtradas[roupas_filtradas['estação'].str.contains("primavera", case=False, na=False)]
            if intensidade == "Média":
                if profundidade == "Claro":
                    return "Primavera Clara", roupas_filtradas[roupas_filtradas['estação'].str.contains("primavera clara", case=False, na=False)]
                return "Outono Puro", roupas_filtradas[roupas_filtradas['estação'].str.contains("outono puro", case=False, na=False)]

        if subtom == "Frio":
            if intensidade == "Alta" and (contraste == "Médio contraste" or contraste == "Baixo contraste escuro"):
                return "Inverno Brilhante", roupas_filtradas[roupas_filtradas['estação'].str.contains("inverno brilhante", case=False, na=False)]
            if intensidade == "Baixa":
                if profundidade == "Claro":
                    return "Verão Suave", roupas_filtradas[roupas_filtradas['estação'].str.contains("verão suave", case=False, na=False)]
                return "Inverno Profundo", roupas_filtradas[roupas_filtradas['estação'].str.contains("inverno profundo", case=False, na=False)]
            if intensidade == 'Média':
                if profundidade == "Claro":
                    return "Verão Claro", roupas_filtradas[roupas_filtradas['estação'].str.contains("verão claro", case=False, na=False)]
                return "Inverno Puro", roupas_filtradas[roupas_filtradas['estação'].str.contains("inverno puro", case=False, na=False)]

        if subtom == "Neutro":
            if profundidade == "Claro":
                return "Verão Suave", roupas_filtradas[roupas_filtradas['estação'].str.contains("verão suave", case=False, na=False)]
            return "Outono Suave", roupas_filtradas[roupas_filtradas['estação'].str.contains("outono suave", case=False, na=False)]

        if subtom == "Oliva":
            if profundidade == "Claro":
                return "Primavera Suave", roupas_filtradas[roupas_filtradas['estação'].str.contains("primavera", case=False, na=False)]
            return "Outono Profundo", roupas_filtradas[roupas_filtradas['estação'].str.contains("outono profundo", case=False, na=False)]

        return "Paleta não identificada", roupas_filtradas

    estacao, roupas_filtradas = classificar_paleta(dicionario)

    # Converte string "[146 28 63]" para lista [146, 28, 63]
    if 'cor bgr' in roupas_filtradas.columns:
        roupas_filtradas["cor bgr"] = roupas_filtradas["cor bgr"].apply(
            lambda x: list(map(int, str(x).strip("[]").split())) if pd.notna(x) else [0, 0, 0]
        )

    # Retorna DataFrame filtrado com estação anotada
    roupas_filtradas = roupas_filtradas.copy()
    roupas_filtradas['__estacao__'] = estacao
    return roupas_filtradas